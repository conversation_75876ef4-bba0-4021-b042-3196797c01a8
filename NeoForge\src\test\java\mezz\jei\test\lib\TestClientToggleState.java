package mezz.jei.test.lib;

import mezz.jei.common.config.IClientToggleState;

public class TestClientToggleState implements IClientToggleState {
	@Override
	public boolean isOverlayEnabled() {
		return true;
	}

	@Override
	public void toggleOverlayEnabled() {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean isEditModeEnabled() {
		return false;
	}

	@Override
	public void toggleEditModeEnabled() {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean isCheatItemsEnabled() {
		return false;
	}

	@Override
	public void toggleCheatItemsEnabled() {
		throw new UnsupportedOperationException();
	}

	@Override
	public void setCheatItemsEnabled(boolean value) {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean isBookmarkOverlayEnabled() {
		return true;
	}

	@Override
	public void toggleBookmarkEnabled() {
		throw new UnsupportedOperationException();
	}

	@Override
	public void setBookmarkEnabled(boolean value) {
		throw new UnsupportedOperationException();
	}

	@Override
	public void addEditModeToggleListener(IEditModeL<PERSON>ener listener) {

	}
}
