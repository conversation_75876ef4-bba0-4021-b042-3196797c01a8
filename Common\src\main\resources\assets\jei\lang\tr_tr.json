{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "JEI Seçenekleri", "jei.tooltip.show.recipes": "<PERSON><PERSON><PERSON><PERSON>", "jei.tooltip.delete.item": "Silmek İçin Tıkla", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "Eşyaları Taşı", "jei.tooltip.recipe.tag": "Herhangi Biri Olur: %s", "jei.tooltip.item.colors": "Renkler: %s", "jei.tooltip.shapeless.recipe": "Şekils<PERSON>", "jei.tooltip.cheat.mode.button.enabled": "<PERSON><PERSON>", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Değiştirmek için %s düğmesine bas.", "jei.tooltip.recipe.by": "%s <PERSON><PERSON>fi", "jei.tooltip.recipe.id": "<PERSON><PERSON><PERSON>: %s", "jei.tooltip.not.enough.space": "Burada içerik listesini gösterecek kadar yer yok.", "jei.tooltip.ingredient.list.disabled": "JEI katmanı kapatıldı.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Açmak için %s düğmesine basın.", "jei.tooltip.bookmarks": "JEI Yer İmleri", "jei.tooltip.bookmarks.usage.nokey": "JEI yer imlerini kontrol etmek için Seçenkelerden kısayol ekleyin.", "jei.tooltip.bookmarks.usage.key": "Bir içeriği yer imlemek için fareyi üzerine götürüp \"%s\" düğmesine basın.", "jei.tooltip.bookmarks.not.enough.space": "Burada yer imlerini gösterecek kadar yer yok.", "jei.tooltip.error.recipe.transfer.missing": "<PERSON>ş<PERSON><PERSON>", "jei.tooltip.error.recipe.transfer.inventory.full": "<PERSON><PERSON><PERSON>", "jei.tooltip.error.recipe.transfer.no.server": "<PERSON><PERSON><PERSON>, JEI modunu kurmalı.", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "<PERSON><PERSON><PERSON>, 2x2 üretim kısmında üretmek için çok büyük.", "jei.tooltip.error.crash": "İpucu hatası, kayıt dosyasına bak.", "jei.chat.error.no.cheat.permission.1": "JEI'nin hile modunu kullanabilmek için yetkin yok.", "key.jei.toggleOverlay": "Eşya Listesi Görünümünü Aç/Kapa", "key.jei.focusSearch": "<PERSON><PERSON>", "key.jei.previousPage": "Önceki Sayfayı Göster", "key.jei.nextPage": "Sonraki <PERSON>fayı Göster", "key.jei.toggleBookmarkOverlay": "<PERSON><PERSON> <PERSON><PERSON>li İçerikleri Göster/Gizle", "key.jei.recipeBack": "<PERSON>nce<PERSON>", "key.jei.previousCategory": "Önceki Tarif <PERSON>", "key.jei.nextCategory": "<PERSON><PERSON><PERSON>", "key.jei.toggleCheatMode": "<PERSON><PERSON>/<PERSON>", "key.jei.toggleEditMode": "Gizle/Düzenle Kipini Değiştir", "key.jei.bookmark": "<PERSON>r İmli İçerik Ekle/Kaldır", "key.jei.showRecipe": "<PERSON><PERSON><PERSON>", "key.jei.showRecipe2": "<PERSON><PERSON><PERSON>", "key.jei.showUses": "Kullanımları Göster", "key.jei.showUses2": "Kullanımları Göster", "jei.config": "JEI Seçenekleri", "jei.config.default": "Varsayılan", "jei.config.valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.title": "%MODNAME Seçenekleri", "jei.config.mode": "Mod", "jei.config.mode.description": "JEI'nin <PERSON>abileceği moda geçin.", "jei.config.mode.cheatItemsEnabled": "<PERSON><PERSON>", "jei.config.mode.cheatItemsEnabled.description": "<PERSON><PERSON>fi göstermek yerine eşyaları ver.", "jei.config.mode.editEnabled": "Eşya Gizleme <PERSON>", "jei.config.mode.editEnabled.description": "Eşya listesinde eşyalara tıklayarak onları göster veya gizle.", "jei.config.interface": "Arayüz", "jei.config.interface.description": "Kullanıcı arayüzü ile ilgili seçenekler.", "jei.config.interface.overlayEnabled": "Eşya Listesi Aktif", "jei.config.interface.overlayEnabled.description": "Eşya listesini açık arayüzün yanında göster.", "jei.config.interface.bookmarkOverlayEnabled": "<PERSON><PERSON> <PERSON>", "jei.config.client.search": "<PERSON><PERSON>", "jei.config.client.search.description": "<PERSON><PERSON> il<PERSON>.", "jei.config.client.search.modNameSearchMode": "@ModAdı", "jei.config.client.search.modNameSearchMode.description": "<PERSON>d ad<PERSON>ı için arama kipi. (öneği: @)", "jei.config.client.search.tooltipSearchMode": "$İpucu", "jei.config.client.search.tooltipSearchMode.description": "İpuçları için arama kipi. (öneği: $)", "jei.config.client.search.tagSearchMode": "Etiket", "jei.config.client.search.tagSearchMode.description": "Etiket adları için arama kipi (önek: #)", "jei.config.client.search.creativeTabSearchMode": "%%YaratıcıSekmesi", "jei.config.client.search.creativeTabSearchMode.description": "Yaratıcı modu sekme adları için arama modu. (öneği: %)", "jei.config.client.search.colorSearchMode": "^Renk", "jei.config.client.search.colorSearchMode.description": "<PERSON><PERSON>ya renkleri için arama modu. (öneği: ^)", "jei.config.client.search.resourceLocationSearchMode": "&KaynakKimliği", "jei.config.client.search.resourceLocationSearchMode.description": "Kaynak ID'leri i<PERSON>in arama modu. (öneği: &)", "jei.config.client.search.searchAdvancedTooltips": "Gelişmiş ipuçlarını ara", "jei.config.client.advanced": "Gelişmiş", "jei.config.client.advanced.description": "JEI fonksiyonlarını değiştirmek için gelişmiş seçenekler.", "jei.config.client.advanced.itemBlacklist": "<PERSON><PERSON><PERSON>", "jei.config.client.advanced.itemBlacklist.description": "Eşya listesinde gösterilmeyecek eşyaların listesi. Biçim: modId[:name[:meta]]. Düzenleme modu bu girdileri otomatik olarak düzenleyecek.", "jei.config.debug.debug.debugMode": "Hata Ayıklama Modu", "jei.config.debug.debug.debugMode.description": "Sadece JEI geliştiricileri için <PERSON>lı, binlerce test eşyası ve birkaç hata ayıklama tarifi ekler.", "jei.config.client.appearance.centerSearch": "<PERSON><PERSON>", "jei.config.client.appearance.centerSearch.description": "JEI arama çubuğunu ekranın alt ortasına yer<PERSON>ştir.", "jei.config.modIdFormat.modName.modNameFormat": "Mod Adı Biçimi", "jei.config.modIdFormat.modName.modNameFormat.description": "JEI arayüz ipuçlarında mod ismi biçimlendirme tipi. Devre dışı bırakmak için boş bırakın.", "jei.config.client.advanced.maxColumns": "<PERSON><PERSON><PERSON>", "jei.config.client.advanced.maxColumns.description": "<PERSON><PERSON><PERSON>nin azami genişliği.", "jei.config.client.appearance.recipeGuiHeight": "<PERSON><PERSON><PERSON> Ta<PERSON>f <PERSON> Yüksekliği", "jei.config.client.appearance.recipeGuiHeight.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>i yü<PERSON>ği", "jei.config.client.cheating.giveMode": "<PERSON>", "jei.config.client.cheating.giveMode.description": "JEI'nin envantere doğrudan eşyaları vermesini mi yoksa fareyle almasını mı istediğinizi seçin.", "gui.jei.editMode.description": "JEI Eşya Listesi Düzenleme Modu:", "gui.jei.editMode.description.hide": "Gizle (%s).", "gui.jei.editMode.description.hide.wild": "Joker ile Gizle (%s).", "gui.jei.category.craftingTable": "<PERSON><PERSON><PERSON>", "gui.jei.category.stoneCutter": "<PERSON><PERSON>", "gui.jei.category.smelting": "Pişirme/Eritme", "gui.jei.category.smoking": "Tütsüleme", "gui.jei.category.blasting": "<PERSON><PERSON>", "gui.jei.category.campfire": "Kamp Ateşinde Pişirme", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.fuel": "Yakıt", "gui.jei.category.fuel.smeltCount.single": "1 eşya pişirir/eritir.", "gui.jei.category.fuel.smeltCount": "%s eşya p<PERSON>r/eritir.", "gui.jei.category.brewing": "<PERSON>ks<PERSON>", "gui.jei.category.brewing.steps": "Adım: %s", "gui.jei.category.itemInformation": "<PERSON><PERSON><PERSON>", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}