# Gradle settings
org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=true
org.gradle.configureondemand=true
org.gradle.caching=true
org.gradle.configuration-cache=true
org.gradle.configuration-cache.problems=warn

# JEI
modName=Just Enough Items
modId=jei
modAuthor=mezz
# http://maven.apache.org/guides/mini/guide-naming-conventions.html
modGroup=mezz.jei
modDescription=JEI is an item and recipe viewing mod for Minecraft, built from the ground up for stability and performance.
# Mojang ships Java 21 to end users in 1.20.5+
modJavaVersion=21
githubUrl=https://github.com/mezz/JustEnoughItems

# Minecraft
minecraftVersion=1.21.7
minecraftVersionRange=[1.21.7, 1.21.8)
# https://projects.neoforged.net/neoforged/neoform
neoformVersionAndTimestamp=1.21.7-20250630.141722
# Earliest version of minecraft that is still compatible
minecraftVersionRangeStart=1.21.7

# Neoforge
# https://projects.neoforged.net/neoforged/neoforge
neoforgeVersion=21.7.15-beta
neoforgeLoaderVersionRange=[4,)
neoforgeVersionRange=[21.7.15-beta,)
neogradle.subsystems.conventions.runs.create-default-run-per-type=false

# Fabric
# https://fabricmc.net/develop/
fabricLoaderVersion=0.16.14
fabricApiVersion=0.129.0+1.21.7
fabricLoaderVersionRange=>=0.16.14
fabricApiVersionRange=>=0.128.2+1.21.7

# Mappings
# https://parchmentmc.org/docs/getting-started
parchmentMinecraftVersion=1.21.5
parchmentVersionFabric=2025.06.15

# Publishing
curseProjectId=238222
curseHomepageUrl=https://www.curseforge.com/minecraft/mc-mods/jei
modrinthId=u6dRKJwZ

# Test
jUnitVersion=5.10.2

# Version
specificationVersion=23.1.0
