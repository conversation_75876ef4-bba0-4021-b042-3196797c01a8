# There are several mandatory fields (#mandatory), and many more that are optional (#optional).
# The overall format is standard TOML format, v0.5.0.
# Note that there are a couple of TOML lists in this file.
# Find more information on toml format here:  https://github.com/toml-lang/toml

# The name of the mod loader type to load - for regular FML @Mod mods it should be javafml
modLoader="javafml" #mandatory

# A version range to match for said mod loader - for regular FML @Mod it will be the minecraft version (without the 1.)
loaderVersion="${neoforgeLoaderVersionRange}" #mandatory

# A URL to refer people to when problems occur with this mod
issueTrackerURL="${githubUrl}/issues?q=is%3Aissue" #optional

# License
license="The MIT License (MIT)"

# A file name (in the root of the mod JAR) containing a logo for display
#logoFile="examplemod.png" #optional

# A text field displayed in the mod UI
#credits="Thanks for this example mod goes to Java" #optional

# A list of mods - how many allowed here is determined by the individual mod loader
[[mods]] #mandatory

# The modid of the mod
modId="${modId}" #mandatory

# The version number of the mod
version="${version}" #mandatory

# A display name for the mod
displayName="${modName}" #mandatory

# A URL for the "homepage" for this mod, displayed in the mod UI
displayURL="${curseHomepageUrl}" #optional

# A URL to query for updates for this mod. See the JSON update specification <here>
#updateJSONURL="" #optional

# A text field displayed in the mod UI
# credits="" #optional

# A text field displayed in the mod UI
authors="${modAuthor}" #optional

# The description text for the mod (multi line!) (#mandatory)
description='''
${modDescription}
'''

# A dependency - use the . to indicate dependency for a specific modid. Dependencies are optional.
[[dependencies.jei]]
    modId="neoforge" #mandatory
    type="required" #mandatory
    versionRange="${neoforgeVersionRange}" #mandatory
    # An ordering relationship for the dependency - BEFORE or AFTER required if the relationship is not mandatory
    ordering="NONE"
    # Side this dependency is applied on - BOTH, CLIENT or SERVER
    side="BOTH"

[[dependencies.jei]]
    modId="minecraft" #mandatory
    type="required" #mandatory
    versionRange="${minecraftVersionRange}" #mandatory
    # An ordering relationship for the dependency - BEFORE or AFTER required if the relationship is not mandatory
    ordering="NONE"
    # Side this dependency is applied on - BOTH, CLIENT or SERVER
    side="BOTH"

[modproperties.jei]
    catalogueImageIcon="jei-icon.png"
