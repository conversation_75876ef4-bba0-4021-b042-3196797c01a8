{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Konfiguration för J<PERSON>", "jei.tooltip.show.recipes": "Visa recept", "jei.tooltip.show.all.recipes.hotkey": "Använd \"%s\" för att visa alla recept", "jei.tooltip.delete.item": "<PERSON><PERSON><PERSON> för att radera", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.liquid.flowing": "%s (rinner)", "jei.tooltip.transfer": "<PERSON><PERSON>", "jei.tooltip.recipe.tag": "Accepterar tagg: %s", "jei.tooltip.item.colors": "Färger: %s", "jei.tooltip.item.search.aliases": "<PERSON><PERSON><PERSON> alias", "jei.tooltip.shapeless.recipe": "<PERSON><PERSON><PERSON><PERSON> recept", "jei.tooltip.cheat.mode.button.enabled": "Fuskläget har aktiverats.", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Tryck på \"%s\" för att slå på/av det.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "Tryck på %s här för att slå på/av det.", "jei.tooltip.recipe.by": "Recept av: %s", "jei.tooltip.recipe.id": "Recept-ID: %s", "jei.tooltip.not.enough.space": "Området till höger om denna meny är för litet för att visa överlägget för JEI:s ingredienslista.", "jei.tooltip.ingredient.list.disabled": "JEI:s överlägg är dolt.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Tryck på %s för att visa det igen.", "jei.tooltip.bookmarks": "JEI-bokmärken", "jei.tooltip.bookmarks.usage.nokey": "Lägg till ett tangentkombination för JEI-bokmärken i dina kontrollinställningar.", "jei.tooltip.bookmarks.usage.key": "Lägg musen över en ingrediens och tryck på \"%s\" för att bokmärka den.", "jei.tooltip.bookmarks.not.enough.space": "Området till vänster om denna meny är för litet för att visa överlägget för JEI:s bokmärken.", "jei.tooltip.bookmarks.recipe": "%s (receptbokmärke)", "jei.tooltip.bookmarks.recipe.add": "Bokmärk detta recept.", "jei.tooltip.bookmarks.recipe.remove": "Ta bort bokmärket för detta recept.", "jei.tooltip.bookmarks.tooltips.usage": "[H<PERSON><PERSON> ned \"%s\" för att visa mer information]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[Tryck på \"%s\" för att tillverka en]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[Tryck på \"%s\" för att tillverka många]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "Visa bokmärkta recept först (aktiverad).", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "Visa bokmärkta recept först (inaktiverad).", "jei.tooltip.recipe.sort.craftable.first.enabled": "Visa tillverkningsbara recept först (aktiverad).", "jei.tooltip.recipe.sort.craftable.first.disabled": "Visa tillverkningsbara recept först (inaktiverad).", "jei.tooltip.error.recipe.transfer.missing": "Föremål saknas", "jei.tooltip.error.recipe.transfer.inventory.full": "<PERSON><PERSON><PERSON><PERSON><PERSON> har för lite utrymme.", "jei.tooltip.error.recipe.transfer.no.server": "Servern måste ha JEI installerat.", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Receptet är för stort för att tillverkas i spelarens rutnät på 2x2.", "jei.tooltip.error.crash": "Denna ingrediens kraschade när dess inforuta hämtades. Se klientloggarna för information.", "jei.tooltip.error.render.crash": "Denna ingrediens kraschade när den renderades. Se klientloggarna för information.", "jei.chat.error.no.cheat.permission.1": "Du har inte behörighet att använda JEI:s fuskläge.", "jei.chat.error.no.cheat.permission.disabled": "Det har inaktiverats för alla spelare på denna server.", "jei.chat.error.no.cheat.permission.enabled": "Följande typer av spelare kan använda det på denna server:", "jei.chat.error.no.cheat.permission.creative": "spelare som är i kreativt läge", "jei.chat.error.no.cheat.permission.op": "spelare som har en operatörsstatus (/op)", "jei.chat.error.no.cheat.permission.give": "spelare som kan använda kommandot \"/give\"", "jei.key.category.overlays": "JEI (överlägg)", "key.jei.toggleOverlay": "Visa/dölj JEI:s överlägg", "key.jei.focusSearch": "Markera sökfältet", "key.jei.previousPage": "Föregående sida", "key.jei.nextPage": "<PERSON><PERSON><PERSON> sida", "key.jei.toggleBookmarkOverlay": "Visa/dölj bokmärkta ingredienser", "jei.key.category.recipe.gui": "JEI (recept)", "key.jei.recipeBack": "Föregående recept", "key.jei.previousCategory": "Föregående receptkategori", "key.jei.nextCategory": "<PERSON><PERSON><PERSON> receptkategori", "key.jei.previousRecipePage": "Föregående receptsida", "key.jei.nextRecipePage": "Nästa receptsida", "key.jei.closeRecipeGui": "Stäng receptgränssnittet", "jei.key.category.cheat.mode": "JEI (fuskläge)", "key.jei.toggleCheatMode": "Slå på/av fuskläge", "key.jei.cheatOneItem": "Fuska fram 1 föremål", "key.jei.cheatOneItem2": "Fuska fram 1 föremål", "key.jei.cheatItemStack": "Fuska fram 1 föremålsgrupp", "key.jei.cheatItemStack2": "Fuska fram 1 föremålsgrupp", "jei.key.category.hover.config.button": "JEI (lägg musen över konfigurationsknappen)", "key.jei.toggleCheatModeConfigButton": "Slå på/av fuskläge", "jei.key.category.edit.mode": "JEI (redigeringsläge)", "key.jei.toggleEditMode": "Dölj/visa ingrediensläge", "key.jei.toggleHideIngredient": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.toggleWildcardHideIngredient": "<PERSON><PERSON><PERSON><PERSON> (med jokertecken)", "jei.key.category.mouse.hover": "JEI (lägg musen över föremål)", "key.jei.bookmark": "<PERSON><PERSON><PERSON> till/ta bort bokmärkt ingrediens", "key.jei.showRecipe": "Visa recept", "key.jei.showRecipe2": "Visa recept", "key.jei.showUses": "Visa användningsområden", "key.jei.showUses2": "Visa användningsområden", "key.jei.transferRecipeBookmark": "Tillverka bokmärkt recept (en gång)", "key.jei.maxTransferRecipeBookmark": "Tillverka bokmärkt recept (många)", "jei.key.category.search": "JEI (sökfilter)", "key.jei.clearSearchBar": "<PERSON><PERSON>", "key.jei.previousSearch": "Föregående sökning", "key.jei.nextSearch": "<PERSON><PERSON><PERSON>", "jei.key.category.dev.tools": "JEI (utvecklingsverktyg)", "key.jei.copy.recipe.id": "Kopiera recept-ID till urklipp", "jei.config": "Konfiguration för J<PERSON>", "jei.config.name": "Namn: %s", "jei.config.description": "Beskrivning: %s", "jei.config.valueValues": "Giltiga värden: %s", "jei.config.defaultValue": "Standardvärde: %s", "jei.config.title": "Konfiguration för %MODNAME", "jei.config.default": "Standard", "jei.config.valid": "Giltiga", "jei.config.mode": "Läge", "jei.config.mode.description": "Ändra läget som JEI fungerar i.", "jei.config.mode.cheatItemsEnabled": "Fuskläge", "jei.config.mode.cheatItemsEnabled.description": "Ge föremål istället för att visa receptet.", "jei.config.mode.editEnabled": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.mode.editEnabled.description": "<PERSON><PERSON><PERSON><PERSON> eller visa ingredienser genom att klicka på dem i ingredienslistan.", "jei.config.interface": "Gränssnitt", "jei.config.interface.description": "Alternativ för användargränssnittet.", "jei.config.interface.overlayEnabled": "Visa ingredienslistan", "jei.config.interface.overlayEnabled.description": "<PERSON><PERSON> listan över ingredienser intill öppet gränssnitt.", "jei.config.interface.bookmarkOverlayEnabled": "Visa bokmärken", "jei.config.interface.bookmarkOverlayEnabled.description": "Visa listan över bokmärken bredvid <PERSON> gränssnitt.", "jei.config.client.appearance": "Utseende", "jei.config.client.appearance.description": "Konfigurationsalternativ för att ändra utseendet i JEI.", "jei.config.client.appearance.centerSearch": "Centrera sökfältet", "jei.config.client.appearance.centerSearch.description": "Placera JEI:s sökfält längst ned i mitten på skärmen.", "jei.config.client.appearance.recipeGuiHeight": "Maximal bredd för receptgränssnittet", "jei.config.client.appearance.recipeGuiHeight.description": "Maximal höjd för receptgränssnittet (i bildpunkter).", "jei.config.client.cheating": "Fusk", "jei.config.client.cheating.description": "Konfigurationsalternativ som relaterar till fusk.", "jei.config.client.cheating.giveMode": "Läge för att fuska fram föremål", "jei.config.client.cheating.giveMode.description": "Välj om JEI ska placera ingredienser direkt i förrådet eller plocka upp dem med musen.", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled": "Fuska fram föremål till föremålsmenyn med kortkommandon", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled.description": "Aktivera att fuska fram föremål till föremålsmenyn med Shift + numeriska tangenter.", "jei.config.client.cheating.showHiddenIngredients": "Visa dolda ingredienser", "jei.config.client.cheating.showHiddenIngredients.description": "Visa ingredienser som inte finns i menyn för kreativt läge.", "jei.config.client.cheating.showTagRecipesEnabled": "Visa taggrecept", "jei.config.client.cheating.showTagRecipesEnabled.description": "Visa recept för ingred<PERSON> som föremåls- och blocktaggar.", "jei.config.client.bookmarks": "Bokmärken", "jei.config.client.bookmarks.description": "Konfigurationsalternativ som relaterar till att bokmärka ingredienser och recept.", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "Placera nya bokmärken längst fram", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "<PERSON><PERSON>r detta aktiveras läggs nya bokmärken till längst fram i bokmärkeslistan. Annars läggs nya bokmärken till i slutet på listan", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled": "<PERSON><PERSON> för att omarrangera bokmärken", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled.description": "Aktivera att dra bokmärken och omarrangera dem i listan.", "jei.config.client.tooltips": "Inforutor", "jei.config.client.tooltips.description": "Konfigurationsalternativ som relaterar till inforutor i JEI.", "jei.config.client.tooltips.bookmarkTooltipFeatures": "Funktionalitet för inforutor till bokmärken", "jei.config.client.tooltips.bookmarkTooltipFeatures.description": "Extra funktionalitet för inforutor till bokmärken.", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures": "Shift för inforutor till bokmärken", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures.description": "<PERSON><PERSON><PERSON> ned Shift för att visa information i inforutor till bokmärken.", "jei.config.client.tooltips.showCreativeTabNamesEnabled": "Visa namn på flikar från kreativt läge", "jei.config.client.tooltips.showCreativeTabNamesEnabled.description": "Visa namn på flikar från kreativt läge i inforutor till ingredienser.", "jei.config.client.tooltips.tagContentTooltipEnabled": "Visa taggarnas innehåll", "jei.config.client.tooltips.tagContentTooltipEnabled.description": "Visa tagginnehåll i inforutor till receptingredienser.", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled": "<PERSON><PERSON><PERSON><PERSON>ll för Hide Single-Ingredient Tag Contents", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled.description": "<PERSON><PERSON><PERSON><PERSON> tagginneh<PERSON>ll i inforutor när det endast finns en ingrediens i taggen.", "jei.config.client.lookups": "<PERSON><PERSON> upp", "jei.config.client.lookups.description": "Konfigurationsalternativ som relaterar till att kolla upp användningsområden och recept för ingredienser i JEI.", "jei.config.client.lookups.lookupFluidContentsEnabled": "Kolla upp vätskeinnehåll", "jei.config.client.lookups.lookupFluidContentsEnabled.description": "<PERSON><PERSON><PERSON> efter recept för vätskor samtidigt som recept med förmål som innehåller vätska.", "jei.config.client.lookups.lookupBlockTagsEnabled": "Kolla upp föremålblockstaggar", "jei.config.client.lookups.lookupBlockTagsEnabled.description": "Sök efter taggar för standardblocken som finns i föremålen samtidigt som föremålstaggar.", "jei.config.client.input": "Inmatning", "jei.config.client.input.description": "Konfigurationsalternativ som relaterar till inmatningar i JEI.", "jei.config.client.input.dragDelayInMilliseconds": "F<PERSON><PERSON>r<PERSON><PERSON><PERSON> för att dra element", "jei.config.client.input.dragDelayInMilliseconds.description": "Antalet millisekunder innan ett långt musklick tolkas som att dra musen.", "jei.config.client.input.smoothScrollRate": "<PERSON><PERSON><PERSON><PERSON> för mjuk rullning", "jei.config.client.input.smoothScrollRate.description": "Rullningshastighet för att rulla mushjulet i mjuka rullningsrutor. Mäts i bildpunkter.", "jei.config.client.performance": "Prestanda", "jei.config.client.performance.description": "Konfigurationsalternativ som relaterar till att prestandaoptimeringar i JEI.", "jei.config.client.performance.lowMemorySlowSearchEnabled": "Sökning med låg minnesanvändning", "jei.config.client.performance.lowMemorySlowSearchEnabled.description": "Ändra till lågminnesläge (det tar längre tid att söka, men använder mindre RAM).", "jei.config.client.advanced": "<PERSON><PERSON><PERSON>", "jei.config.client.advanced.description": "Avancerade konfigurationsalternativ för att ändra hur JEI fungerar.", "jei.config.client.advanced.catchRenderErrorsEnabled": "Fånga renderingsfel", "jei.config.client.advanced.catchRenderErrorsEnabled.description": "Fånga renderingsfel från moddade ingredienser och försök att återhämta från dem istället för att krascha.", "jei.config.client.sorting": "Sortering", "jei.config.client.sorting.description": "Konfigurationsalternativ som relaterar till hur JEI sorterar recept och ingredienser.", "jei.config.client.sorting.ingredientSortStages": "Sorteringssteg för ing<PERSON>", "jei.config.client.sorting.ingredientSortStages.description": "Sorteringsordning för ing<PERSON>.", "jei.config.client.sorting.recipeSorterStages": "Sorteringssteg för recept", "jei.config.client.sorting.recipeSorterStages.description": "Sorteringsordning för recept som visas.", "jei.config.client.search": "S<PERSON><PERSON>ning", "jei.config.client.search.description": "Konfigurationsalternativ som relaterar till hur JEI söker efter recept.", "jei.config.client.search.modNameSearchMode": "Sökläge för @modnamn", "jei.config.client.search.modNameSearchMode.description": "Sökläge för namn på moddar (prefix: @).", "jei.config.client.search.tagSearchMode": "Sökläge för #taggar", "jei.config.client.search.tagSearchMode.description": "Sökläge för tag<PERSON>n (prefix: #).", "jei.config.client.search.tooltipSearchMode": "Sökläge för $inforutor", "jei.config.client.search.tooltipSearchMode.description": "Sökläge för inforutor (prefix: $).", "jei.config.client.search.colorSearchMode": "Sökläge för ^färger", "jei.config.client.search.colorSearchMode.description": "Sökläge för färger (prefix: ^).", "jei.config.client.search.resourceLocationSearchMode": "Sökläge för &resursplatser", "jei.config.client.search.resourceLocationSearchMode.description": "Sökläge för resursernas platser (prefix: &).", "jei.config.client.search.creativeTabSearchMode": "%Sökläge för %fliknamn från kreativt läge.", "jei.config.client.search.creativeTabSearchMode.description": "Sökläge för fliknamn från kreativt läge (prefix: %).", "jei.config.client.search.searchAdvancedTooltips": "Sök i avancerade inforutor", "jei.config.client.search.searchAdvancedTooltips.description": "Sök i avancerade inforutor (visas med F3 + H).", "jei.config.client.search.searchModIds": "<PERSON><PERSON><PERSON> efter mod-ID", "jei.config.client.search.searchModIds.description": "<PERSON><PERSON>k efter mod-ID:n samtidigt som modnamn.", "jei.config.client.search.searchModAliases": "<PERSON><PERSON><PERSON> efter modalias", "jei.config.client.search.searchModAliases.description": "<PERSON><PERSON><PERSON> efter modalias (alternativa namn) som läggs till av till<PERSON><PERSON>, såv<PERSON>l som modnamn.", "jei.config.client.search.searchShortModNames": "<PERSON><PERSON><PERSON> efter korta modnamn", "jei.config.client.search.searchShortModNames.description": "Sök efter de första bokstäverna i ett modnamn.", "jei.config.client.search.searchIngredientAliases": "<PERSON><PERSON><PERSON> efter ingred<PERSON>", "jei.config.client.search.searchIngredientAliases.description": "<PERSON><PERSON><PERSON> efter ingrediensalias (alternativa namn) som läggs till av till<PERSON><PERSON>, såv<PERSON>l som ingrediensnamn.", "jei.config.client.ingredientList": "Ingredienslista", "jei.config.client.ingredientList.description": "Konfigurationsalternativ som relaterar till ingredienslistan (listan över ingredienser på höger sida av skärmen)", "jei.config.client.ingredientList.maxRows": "Max antal rader", "jei.config.client.ingredientList.maxRows.description": "Maximalt antal rader som visas.", "jei.config.client.ingredientList.maxColumns": "<PERSON> antal kolunmer", "jei.config.client.ingredientList.maxColumns.description": "Max antal kolumner som visas.", "jei.config.client.ingredientList.horizontalAlignment": "Horisontal justering", "jei.config.client.ingredientList.horizontalAlignment.description": "Horisontal justering av ingredienslistan inuti det tillgängliga området.", "jei.config.client.ingredientList.verticalAlignment": "<PERSON><PERSON><PERSON><PERSON> justering", "jei.config.client.ingredientList.verticalAlignment.description": "Vertikal justering av ingredienslistan inuti det tillgängliga området.", "jei.config.client.ingredientList.buttonNavigationVisibility": "Visa navigering", "jei.config.client.ingredientList.buttonNavigationVisibility.description": "Visa knappar ovanför sidorna. Använd AUTO_HIDE för att endast visa dem när det finns flera sidor.", "jei.config.client.ingredientList.drawBackground": "Rendera gränssnittsbakgrund", "jei.config.client.ingredientList.drawBackground.description": "Aktivera detta för att rendera en bakgrundstextur bakom ingredienslistan.", "jei.config.client.bookmarkList": "Bokmärkeslista", "jei.config.client.bookmarkList.description": "Konfigurationsalternativ som relaterar till bokmärkeslistan (listan över bokmärkta ingredienser på vänster sida av skärmen)", "jei.config.client.bookmarkList.maxRows": "Max antal rader", "jei.config.client.bookmarkList.maxRows.description": "Max antal rader som visas.", "jei.config.client.bookmarkList.maxColumns": "<PERSON> antal kolumner", "jei.config.client.bookmarkList.maxColumns.description": "Max antal kolumner som visas.", "jei.config.client.bookmarkList.horizontalAlignment": "Horisontal justering", "jei.config.client.bookmarkList.horizontalAlignment.description": "Horisontal justering av bokmärkeslistan inuti det tillgängliga området.", "jei.config.client.bookmarkList.verticalAlignment": "<PERSON><PERSON><PERSON><PERSON> justering", "jei.config.client.bookmarkList.verticalAlignment.description": "Vertikal justering av bokmärkeslistan inuti det tillgängliga området.", "jei.config.client.bookmarkList.buttonNavigationVisibility": "Visa navigering", "jei.config.client.bookmarkList.buttonNavigationVisibility.description": "Visa knappar ovanför sidorna. Använd AUTO_HIDE för att endast visa dem när det finns flera sidor.", "jei.config.client.bookmarkList.drawBackground": "Rendera gränssnittsbakgrund", "jei.config.client.bookmarkList.drawBackground.description": "Aktivera detta för att rendera en bakgrundstextur bakom bokmärkeslistan.", "jei.config.client.advanced.itemBlacklist": "<PERSON><PERSON><PERSON><PERSON> för <PERSON>", "jei.config.client.advanced.itemBlacklist.description": "Lista över ingredienser som inte bör visas i ingredienslistan. Format: modID[:namn[:meta]]. Läget för att dölja ingredienser kommer automatiskt att lägga till eller ta bort element här.", "jei.config.client.advanced.maxColumns": "Maximal bredd för <PERSON>", "jei.config.client.advanced.maxColumns.description": "Maximal bredd för <PERSON> till ingrediens- och bokmärkeslistan.", "jei.config.modIdFormat.modName": "Modnamn", "jei.config.modIdFormat.modName.description": "Konfigurationsalternativ som relaterar till att  displaying Mod Names", "jei.config.modIdFormat.modName.modNameFormat": "Namnformat för moddar", "jei.config.modIdFormat.modName.modNameFormat.description": "Hur modnamnet bör formateras i inforutan för JEI:s gränssnitt. Lämna tomt för att inaktivera.", "jei.config.debug.debug": "Felsökning", "jei.config.debug.debug.description": "Config options to help developers debug issues in JEI", "jei.config.debug.debug.debugMode": "Felsökningsläge", "jei.config.debug.debug.debugMode.description": "Endast användbart för J<PERSON><PERSON><PERSON>, lägger till testingredienser och några felsökningsrecept.", "jei.config.debug.debug.debugGuis": "Gränssnitt för f<PERSON>", "jei.config.debug.debug.debugGuis.description": "Aktivera gränssnittsläge för felsökning", "jei.config.debug.debug.debugInputs": "Felsökningsinmatning", "jei.config.debug.debug.debugInputs.description": "Aktivera inmatningsläge för felsökning", "jei.config.debug.debug.debugInfoTooltipsEnabled": "Felsökningsinformation i inforutor", "jei.config.debug.debug.debugInfoTooltipsEnabled.description": "Lägg till felsökningsinformation i inforrutor för ingredienser när avancerade inforutor har aktiverats.", "jei.config.debug.debug.crashingTestItemsEnabled": "Aktivera kraschande testföremål", "jei.config.debug.debug.crashingTestItemsEnabled.description": "Lägger till ingredienser i JEI som avsiktligt kraschar för att underlätta felsökning i JEI.", "jei.config.debug.debug.logSuffixTreeStats": "Logga statistik om sökträd", "jei.config.debug.debug.logSuffixTreeStats.description": "Loggar information om suffixträd som används i sökningar för att underlätta felsökning i JEI.", "jei.config.colors.colors": "<PERSON><PERSON><PERSON>", "jei.config.colors.colors.description": "Konfigurationsalternativ som relaterar till att söka efter föremålsfärger i JEI", "jei.config.colors.colors.searchColors": "<PERSON><PERSON><PERSON> efter färger", "jei.config.colors.colors.searchColors.description": "Färgvärden att söka efter.", "gui.jei.editMode.description": "<PERSON><PERSON><PERSON> för att dölja ingred<PERSON>:", "gui.jei.editMode.description.hide": "Tryck på \"%s\" för att dölja", "gui.jei.editMode.description.hide.wild": "Tryck på \"%s\" för att dölja med jokertecken.", "gui.jei.category.craftingTable": "Tillverkning", "gui.jei.category.stoneCutter": "Stenskärning", "gui.jei.category.smelting": "Smältning", "gui.jei.category.smoking": "R<PERSON><PERSON>ning", "gui.jei.category.blasting": "Smältning", "gui.jei.category.campfire": "Lägereldstillagning", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%s s", "gui.jei.category.smelting_fuel": "Smältningsbränsle", "gui.jei.category.smoking_fuel": "Rökningsbränsle", "gui.jei.category.blasting_fuel": "Smältningsbränsle", "gui.jei.category.fuel.smeltCount.single": "Smälter 1 föremål", "gui.jei.category.fuel.smeltCount": "Smälter %s föremål", "gui.jei.category.brewing": "B<PERSON><PERSON><PERSON>", "gui.jei.category.brewing.steps": "Steg: %s", "gui.jei.category.compostable": "Kompostering", "gui.jei.category.compostable.chance": "Chans: %s %%", "gui.jei.category.itemInformation": "Information", "gui.jei.category.tagInformation": "%s taggar", "gui.jei.category.tagInformation.block": "Blocktaggar", "gui.jei.category.tagInformation.fluid": "Vätsketaggar", "gui.jei.category.tagInformation.item": "Föremålstaggar", "gui.jei.category.recipe.crashed": "Detta förmål krashade. Se klientloggen för information.", "jei.message.configured": "Installera modden \"Configured\" för att få åtkomst till konfigurationen i spelet", "jei.message.config.folder": "<PERSON>r klicka här istället för att öppna konfigurationsmappen för JEI", "jei.message.copy.recipe.id.success": "Följande recept-ID kopierades till urklipp: %s", "jei.message.copy.recipe.id.failure": "Misslyckades att kopiera recept-ID till urklipp eftersom ID:et är okänt", "jei.message.missing.recipes.from.server": "JEI saknar recept. Installera JEI på servern för att synkronisera recepten till klienten.\nSedan Minecraft 1.21.2 lagras recepten på servern och inte i klienten.", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "jei.key.shift": "SHIFT", "jei.key.mouse.left": "KLICK", "jei.key.mouse.right": "HÖGERKLICK", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}