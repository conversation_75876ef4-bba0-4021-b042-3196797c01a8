{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "JEI-Konfiguration", "jei.tooltip.show.recipes": "Zeige Rezepte", "jei.tooltip.delete.item": "Klicke zum Löschen", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "Bewege Items", "jei.tooltip.recipe.tag": "Akzeptiert: %s", "jei.tooltip.item.colors": "Farben: %s", "jei.tooltip.shapeless.recipe": "Formloses Rezept", "jei.tooltip.cheat.mode.button.enabled": "Cheat-Modus", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Klicke auf %s zum Umschalten.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s hier zum Umschalten.", "jei.tooltip.recipe.by": "Re<PERSON><PERSON> von: %s", "jei.tooltip.recipe.id": "Rezept-ID: %s", "jei.tooltip.not.enough.space": "Hier ist nicht genug Platz zum Anzeigen der Zutaten-Liste.", "jei.tooltip.ingredient.list.disabled": "Die JEI-Oberfläche ist deaktiviert.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Klicke auf %s zum Aktivieren.", "jei.tooltip.bookmarks": "JEI <PERSON>ez<PERSON>", "jei.tooltip.bookmarks.usage.nokey": "Füge eine Tastenkombination für JEI-Lesezeichen in der Steuerungs-Einstellung hinzu.", "jei.tooltip.bookmarks.usage.key": "Gehe mit den Mauszeiger auf einen Gegenstand und drücke \"%s\" um es als Lesezeichen zu speichern.", "jei.tooltip.bookmarks.not.enough.space": "Dort ist nicht genug Platz, um das Lesezeichen hier anzeigen zukönnen.", "jei.tooltip.error.recipe.transfer.missing": "Fehlende Items", "jei.tooltip.error.recipe.transfer.inventory.full": "Inventar ist zu voll", "jei.tooltip.error.recipe.transfer.no.server": "<PERSON><PERSON> dem <PERSON> muss JEI installiert sein", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Rezept ist zu groß für 2x2", "jei.tooltip.error.crash": "<PERSON><PERSON> des Tooltips, <PERSON><PERSON><PERSON> (Log)", "jei.chat.error.no.cheat.permission.1": "<PERSON>r ist es nicht erlaubt, JEI's Cheat-Modus zu benutzen.", "jei.chat.error.no.cheat.permission.disabled": "<PERSON><PERSON> <PERSON>, ist der JEI Cheat Mode für alle Spieler deaktiviert.", "jei.chat.error.no.cheat.permission.enabled": "<PERSON><PERSON>, können folgende Spieler den JEI Cheat Mode benutzen:", "jei.chat.error.no.cheat.permission.creative": "Spieler im Kreativmodus", "jei.chat.error.no.cheat.permission.op": "S<PERSON>ler die Operator sind (/op)", "jei.chat.error.no.cheat.permission.give": "Spieler die berechtigt sind für /give", "jei.key.category.overlays": "JEI (Overlays)", "key.jei.toggleOverlay": "Item-Liste (de-)aktivieren", "key.jei.focusSearch": "<PERSON><PERSON>", "key.jei.previousPage": "Vorherige Seite anzeigen", "key.jei.nextPage": "Nächste Seite anzeigen", "key.jei.toggleBookmarkOverlay": "Zeige/Verstecke den mit Lesezeichen versehenen Gegenstand", "jei.key.category.recipe.gui": "JEI (Rezepte)", "key.jei.recipeBack": "Zuvor angeschautes Rezept anzeigen", "key.jei.previousCategory": "Vorherige Rezeptkategorie anzeigen", "key.jei.nextCategory": "Nächste Rezeptkategorie anzeigen", "key.jei.previousRecipePage": "Vorherige Rezeptseite anzeigen", "key.jei.nextRecipePage": "Nächste Rezeptseite anzeigen", "jei.key.category.cheat.mode": "JEI (Cheat Modus)", "key.jei.toggleCheatMode": "Cheat Modus um<PERSON>ten", "key.jei.cheatOneItem": "Cheate 1 Item", "key.jei.cheatOneItem2": "Cheate 1 Item", "key.jei.cheatItemStack": "Cheate 1 Item-Stapel", "key.jei.cheatItemStack2": "Cheate 1 Item-Stapel", "jei.key.category.hover.config.button": "JEI (wenn Maus über Config Button schwebt)", "key.jei.toggleCheatModeConfigButton": "Cheat Modus um<PERSON>ten", "jei.key.category.edit.mode": "JEI (Editier <PERSON>)", "key.jei.toggleEditMode": "Zutaten Ausblendungsmodus umschalten", "key.jei.toggleHideIngredient": "Zutat ausblenden", "key.jei.toggleWildcardHideIngredient": "<PERSON><PERSON>t ausblenden per Wildcard Übereinstimmung", "jei.key.category.mouse.hover": "JEI (<PERSON><PERSON>)", "key.jei.bookmark": "Hinzufügen/Entfernen Lesezeichen", "key.jei.showRecipe": "Rezept anzeigen", "key.jei.showRecipe2": "Rezept anzeigen", "key.jei.showUses": "Verwendung anzeigen", "key.jei.showUses2": "Verwendung anzeigen", "jei.key.category.search": "JEI (Suchfilter)", "key.jei.clearSearchBar": "<PERSON><PERSON><PERSON>", "key.jei.previousSearch": "Vorherige Suche im Verlauf", "key.jei.nextSearch": "Nächste Suche im Verlauf", "jei.config": "JEI-Konfiguration", "jei.config.default": "Standard", "jei.config.valid": "Zulässig", "jei.config.title": "%MODNAME-Konfiguration", "jei.config.mode": "Modus", "jei.config.mode.description": "<PERSON><PERSON><PERSON> den Modus, in dem sich JEI befindet", "jei.config.mode.cheatItemsEnabled": "Aktiviere Item-Cheating", "jei.config.mode.cheatItemsEnabled.description": "<PERSON><PERSON><PERSON> dir das Item, statt dessen Rezept anzuzeigen.", "jei.config.mode.editEnabled": "Aktiviere den Listeneditiermodus", "jei.config.mode.editEnabled.description": "Verstecke und zeige Items durch Klicken in der Item-Liste.", "jei.config.interface": "Oberfläche", "jei.config.interface.description": "Die Benutzeroberfläche betreffende Optionen", "jei.config.interface.overlayEnabled": "Aktiviere die Item-Liste", "jei.config.interface.overlayEnabled.description": "Zeige die Liste der Items neben einer geöffneten Benutzeroberfläche.", "jei.config.interface.bookmarkOverlayEnabled": "<PERSON><PERSON><PERSON>", "jei.config.interface.bookmarkOverlayEnabled.description": "Zeige die Lesezeichen-Liste neben der Benutzeroberfläche.", "jei.config.client.search": "<PERSON><PERSON>", "jei.config.client.search.description": "Suchleisteneinstellungen", "jei.config.client.search.modNameSearchMode": "@ModName", "jei.config.client.search.modNameSearchMode.description": "Suchmodus für den Mod-Namen (Präfix: @)", "jei.config.client.search.tooltipSearchMode": "$Tooltip", "jei.config.client.search.tooltipSearchMode.description": "Suchmodus für den Tooltip (Präfix: $)", "jei.config.client.search.tagSearchMode": "#Tag", "jei.config.client.search.tagSearchMode.description": "Suchmodus für den Tag-Namen (Präfix: #)", "jei.config.client.search.creativeTabSearchMode": "%%Kategorie", "jei.config.client.search.creativeTabSearchMode.description": "Suchmodus für die Kategorien im Kreativmodus-Inventar (Präfix: %)", "jei.config.client.search.colorSearchMode": "^Farbe", "jei.config.client.search.colorSearchMode.description": "Suchmodus für die Item-Farben (Präfix: ^)", "jei.config.client.search.resourceLocationSearchMode": "&ResourceLocation", "jei.config.client.search.resourceLocationSearchMode.description": "Suchmodus für die \"ResourceLocation\" (prefix: &)", "jei.config.client.search.searchAdvancedTooltips": "Durchsuche erweiterte Tooltips", "jei.config.client.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.client.advanced.description": "Erweiterte Konfigurationsoptionen, die ändern, wie JEI funktioniert.", "jei.config.client.advanced.itemBlacklist": "Aktivier<PERSON>", "jei.config.client.advanced.itemBlacklist.description": "Items, die nicht angezeigt werden sollen. Format: modId[:name[:meta]]. Der Listeneditiermodus entfernt und fügt automatisch neue Items zu dieser Liste hinzu.", "jei.config.debug.debug.debugMode": "Debug-Modus aktivieren", "jei.config.debug.debug.debugMode.description": "Fügt tausende Test-Items und einige Debug-Rezepte hinzu. Nur für Entwickler bestimmt.", "jei.config.client.appearance.centerSearch": "Zentriere Suchleiste", "jei.config.client.appearance.centerSearch.description": "Die JEI-Suchleiste wird unten in der Mitte des Bildschirms angezeigt.", "jei.config.modIdFormat.modName.modNameFormat": "Mod-Namensformat", "jei.config.modIdFormat.modName.modNameFormat.description": "Wie der Mod-Name in dem Tooltip für die JEI-Oberfläche formatiert werden soll. Zum Deaktivieren einfach leer lassen.", "jei.config.client.advanced.maxColumns": "Maximale Breite", "jei.config.client.advanced.maxColumns.description": "Die maximale Breite der Zutaten-Liste.", "jei.config.client.appearance.recipeGuiHeight": "Max<PERSON> Rezepthöhe der Benutzeroberfläche", "jei.config.client.appearance.recipeGuiHeight.description": "Die maximale höhe der Rezepten-Benutzeroberfläche.", "jei.config.client.cheating.giveMode": "Item-Aufnahmemodus", "jei.config.client.cheating.giveMode.description": "<PERSON><PERSON><PERSON><PERSON>, ob J<PERSON> die Items direkt in das Inventar geben soll oder du sie mit der Maus aufnimmst.", "gui.jei.editMode.description": "JEI Zutaten Ausblendungsmodus:", "gui.jei.editMode.description.hide": "Ausblenden mit %s.", "gui.jei.editMode.description.hide.wild": "Ausblenden per Wildcard mit %s.", "gui.jei.category.craftingTable": "Fertigungsrezepte", "gui.jei.category.stoneCutter": "Steinsägerezepte", "gui.jei.category.smelting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.smoking": "Räucherofenrezepte", "gui.jei.category.blasting": "Schmelzofenrezepte", "gui.jei.category.campfire": "Lagerfeuerrezepte", "gui.jei.category.smelting.experience": "%s EP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.fuel": "Kraftstoffrezepte", "gui.jei.category.fuel.smeltCount.single": "Schmilzt 1 Item", "gui.jei.category.fuel.smeltCount": "Schmilzt %s Items", "gui.jei.category.brewing": "Braurezepte", "gui.jei.category.brewing.steps": "Schritte: %s", "gui.jei.category.compostable": "Kompostierbar", "gui.jei.category.compostable.chance": "Chance: %s%%", "gui.jei.category.itemInformation": "Beschreibung", "jei.message.configured": "Installier den \"Configured\" mod um auf die In-Game Konfiguration zu zugreifen", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}