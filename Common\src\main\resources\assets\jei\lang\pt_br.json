{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Configurações do JEI", "jei.tooltip.show.recipes": "Mostrar receitas", "jei.tooltip.delete.item": "Clique para excluir", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "Transferir itens", "jei.tooltip.recipe.tag": "<PERSON>ita qualquer: %s", "jei.tooltip.item.colors": "Cores: %s", "jei.tooltip.shapeless.recipe": "Receita indefinida", "jei.tooltip.cheat.mode.button.enabled": "Modo trapaça ativado", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Pressione %s para ativá-lo.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s aqui para ativá-lo.", "jei.tooltip.recipe.by": "Receita por: %s", "jei.tooltip.recipe.id": "ID da receita: %s", "jei.tooltip.not.enough.space": "Não há espaço suficiente para exibir o JEI aqui.", "jei.tooltip.ingredient.list.disabled": "A sobreposição do JEI está desativada.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Pressione %s para ativá-la.", "jei.tooltip.bookmarks": "<PERSON><PERSON><PERSON><PERSON>", "jei.tooltip.bookmarks.usage.nokey": "Definir tecla de atalho para itens favoritos do JEI em Controles.", "jei.tooltip.bookmarks.usage.key": "Passe o cursor sobre um ingrediente e pressione \"%s\" para adicioná-lo aos favoritos.", "jei.tooltip.bookmarks.not.enough.space": "Não há espaço suficiente para exibir os favoritos aqui.", "jei.tooltip.error.recipe.transfer.missing": "<PERSON>ens ausentes", "jei.tooltip.error.recipe.transfer.inventory.full": "O inventário está muito cheio", "jei.tooltip.error.recipe.transfer.no.server": "O servidor deve ter o JEI instalado", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Não é possível fabricar isso numa grade 2x2.", "jei.tooltip.error.crash": "Descrição do erro, ver registro", "jei.chat.error.no.cheat.permission.1": "Você não tem permissão para usar o modo trapaça do JEI.", "key.jei.toggleOverlay": "Exibir/ocultar JEI", "key.jei.focusSearch": "Selecionar barra de busca", "key.jei.previousPage": "Voltar", "key.jei.nextPage": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.toggleBookmarkOverlay": "Exibir/ocultar ingredientes favoritos", "key.jei.recipeBack": "Mostrar receita anterior", "key.jei.toggleCheatMode": "Alternar modo trapaça", "key.jei.toggleEditMode": "Alternar modo edição/ocultar", "key.jei.bookmark": "Adic./remov. ingrediente favorito", "key.jei.showRecipe": "Mostrar receita", "key.jei.showRecipe2": "Mostrar receita", "key.jei.showUses": "Mostrar usos", "key.jei.showUses2": "Mostrar usos", "jei.config": "Configurações do JEI", "jei.config.default": "Padrão", "jei.config.valid": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.title": "Config. do %MODNAME", "jei.config.mode": "Modo", "jei.config.mode.description": "Mudar o modo de operação do JEI.", "jei.config.mode.cheatItemsEnabled": "Modo trapaça ativado", "jei.config.mode.cheatItemsEnabled.description": "Dar itens em vez de apenas mostrar a receita.", "jei.config.mode.editEnabled": "Ocultar ingredientes", "jei.config.mode.editEnabled.description": "<PERSON><PERSON><PERSON><PERSON> ou mostrar ingredientes ao clicá-los na lista.", "jei.config.interface": "Interface", "jei.config.interface.description": "Opções de interface.", "jei.config.interface.overlayEnabled": "Mostrar lista de ingredientes", "jei.config.interface.overlayEnabled.description": "Exibe a lista de itens próxima à interface.", "jei.config.interface.bookmarkOverlayEnabled": "<PERSON><PERSON><PERSON> favoritos", "jei.config.interface.bookmarkOverlayEnabled.description": "Mostrar a lista de favoritos próxima às interfaces abertas.", "jei.config.client.search": "Opções de busca", "jei.config.client.search.description": "Opções da barra de busca.", "jei.config.client.search.modNameSearchMode": "@ModName", "jei.config.client.search.modNameSearchMode.description": "Modo de busca para Mod Names (prefixo: @)", "jei.config.client.search.tooltipSearchMode": "$Tooltip", "jei.config.client.search.tooltipSearchMode.description": "Modo de busca para Tooltips (prefixo: $)", "jei.config.client.search.tagSearchMode": "#Tag", "jei.config.client.search.tagSearchMode.description": "Modo de pesquisa para nomes de tags (prefix: #)", "jei.config.client.search.creativeTabSearchMode": "%%CreativeModeTab", "jei.config.client.search.creativeTabSearchMode.description": "Modo de busca para Creative Tab Names (prefixo: %)", "jei.config.client.search.colorSearchMode": "^Color", "jei.config.client.search.colorSearchMode.description": "Modo de busca para Colors (prefixo: ^)", "jei.config.client.search.resourceLocationSearchMode": "&ResourceLocation", "jei.config.client.search.resourceLocationSearchMode.description": "Modo de busca para IDs de recursos (prefixo: &)", "jei.config.client.search.searchAdvancedTooltips": "Pesquisar dicas de ferramentas avançadas", "jei.config.client.advanced": "Avançado", "jei.config.client.advanced.description": "Configurações avançadas do JEI.", "jei.config.client.advanced.itemBlacklist": "Lista negra de ingredientes", "jei.config.client.advanced.itemBlacklist.description": "Lista de ingredientes que não deve ser exibida na lista principal. Formato: modId[:name[:meta]]. O modo ingredientes ocultos adicionará ou removerá entradas aqui automaticamente.", "jei.config.debug.debug.debugMode": "Depuração", "jei.config.debug.debug.debugMode.description": "Recomendado apenas para desenvolvedores do JEI. Adiciona ingredientes de testes e algumas receitas de depuração.", "jei.config.client.appearance.centerSearch": "Centralizar barra de busca", "jei.config.client.appearance.centerSearch.description": "Mover a barra de busca do JEI para o centro-inferior da tela.", "jei.config.modIdFormat.modName.modNameFormat": "Formato de nome do JEI", "jei.config.modIdFormat.modName.modNameFormat.description": "Mudar o formato do nome em descrições nas interfaces do JEI. Deixe em branco para desativar.", "jei.config.client.advanced.maxColumns": "Largura máxima de sobreposição", "jei.config.client.advanced.maxColumns.description": "A largura máxima da lista de ingredientes.", "jei.config.client.appearance.recipeGuiHeight": "Altura máxima da interface de receitas", "jei.config.client.appearance.recipeGuiHeight.description": "A altura máxima para a interface de receitas.", "jei.config.client.cheating.giveMode": "Modo <PERSON>", "jei.config.client.cheating.giveMode.description": "Permitir que o JEI envie itens diretamente ao inventário ou possa obtê-los com o mouse.", "gui.jei.editMode.description": "Modo ingredientes ocultos do JEI:", "gui.jei.editMode.description.hide": "%s para ocultar", "gui.jei.category.craftingTable": "Fabricação", "gui.jei.category.stoneCutter": "Corte de Pedra", "gui.jei.category.smelting": "Fundição", "gui.jei.category.smoking": "Defumação", "gui.jei.category.blasting": "Explosão", "gui.jei.category.campfire": "<PERSON><PERSON><PERSON>", "gui.jei.category.smelting.experience": "%s de exp.", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.fuel": "Combustível", "gui.jei.category.fuel.smeltCount.single": "Funde 1 item", "gui.jei.category.fuel.smeltCount": "Funde %s itens", "gui.jei.category.brewing": "Poções", "gui.jei.category.brewing.steps": "Passos: %s", "gui.jei.category.itemInformation": "Informações", "jei.message.configured": "Instale o mod \"Configurado\" para acessar a configuração do jogo", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Portas de madeira permitem que você bloqueie a entrada de monstros em seu prédio.\\nTeste sentenças..", "description.jei.wooden.door.2": "Clicar em uma porta muda seu estado de aberto para fechado e vice-versa.", "description.jei.wooden.door.3": "As portas de madeira podem ser abertas/fechadas através dos circuitos de Redstone.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}