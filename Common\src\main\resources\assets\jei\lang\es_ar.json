{"_comment": "Debug (modo de depuración, no requiere traducción)", "jei.tooltip.config": "Configuración de JEI", "jei.tooltip.show.recipes": "Mostrar recetas", "jei.tooltip.show.all.recipes.hotkey": "Presioná \"%s\" para mostrar todas las recetas.", "jei.tooltip.delete.item": "Hacé clic para eliminar.", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.liquid.flowing": "%s (En flujo)", "jei.tooltip.transfer": "Mover <PERSON><PERSON>s", "jei.tooltip.recipe.tag": "Acepta etiqueta: %s", "jei.tooltip.item.colors": "Colores: %s", "jei.tooltip.item.search.aliases": "Alias de búsqueda:", "jei.tooltip.shapeless.recipe": "<PERSON>ceta sin orden", "jei.tooltip.cheat.mode.button.enabled": "Modo trampa activado.", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Presioná \"%s\" para desactivarlo.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "Presioná \"%s\" acá para desactivarlo.", "jei.tooltip.recipe.by": "Receta de: %s", "jei.tooltip.recipe.id": "ID de receta: %s", "jei.tooltip.not.enough.space": "El área a la derecha de esta pantalla es demasiado chica para mostrar la lista de ingredientes de JEI.", "jei.tooltip.ingredient.list.disabled": "Los overlays de JEI están ocultos.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Presioná \"%s\" para volver a mostrarlos.", "jei.tooltip.bookmarks": "Favoritos de JEI", "jei.tooltip.bookmarks.usage.nokey": "Asigná una tecla para los favoritos de JEI en los controles.", "jei.tooltip.bookmarks.usage.key": "Pasá el cursor sobre un ingrediente y presioná \"%s\" para marcarlo como favorito.", "jei.tooltip.bookmarks.not.enough.space": "El área a la izquierda de esta pantalla es demasiado chica para mostrar la lista de favoritos de JEI.", "jei.tooltip.bookmarks.recipe": "Receta favorita: %s", "jei.tooltip.bookmarks.recipe.add": "Agregar esta receta a favoritos.", "jei.tooltip.bookmarks.recipe.remove": "Quitar esta receta de favoritos.", "jei.tooltip.bookmarks.tooltips.usage": "[Presioná \"%s\" para ver detalles]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[Presioná \"%s\" para fabricar uno]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[Presioná \"%s\" para fabricar varios]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "Mostrar recetas favoritas primero (activado).", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "Mostrar recetas favoritas primero (desactivado).", "jei.tooltip.recipe.sort.craftable.first.enabled": "Mostrar recetas fabricables primero (activado).", "jei.tooltip.recipe.sort.craftable.first.disabled": "Mostrar recetas fabricables primero (desactivado).", "jei.tooltip.error.recipe.transfer.missing": "<PERSON><PERSON><PERSON>", "jei.tooltip.error.recipe.transfer.inventory.full": "El inventario está lleno.", "jei.tooltip.error.recipe.transfer.no.server": "El servidor debe tener JEI instalado.", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "La receta es demasiado grande para fabricarse en la cuadrícula 2x2 del jugador.", "jei.tooltip.error.crash": "Este ingrediente generó un error al mostrar su información. Revisá los registros del cliente para más detalles.", "jei.tooltip.error.render.crash": "Este ingrediente generó un error al renderizarse. Revisá los registros del cliente para más detalles.", "jei.chat.error.no.cheat.permission.1": "No tenés permiso para usar el modo trampa de JEI.", "jei.chat.error.no.cheat.permission.disabled": "En este servidor, está desactivado para todos los jugadores.", "jei.chat.error.no.cheat.permission.enabled": "En este servidor, solo pueden usarlo los siguientes tipos de jugadores:", "jei.chat.error.no.cheat.permission.creative": "jugadores en modo creativo", "jei.chat.error.no.cheat.permission.op": "jugadores con estado de operador (/op)", "jei.chat.error.no.cheat.permission.give": "jugadores que pueden usar el comando \"/give\"", "jei.key.category.overlays": "JEI (Overlays)", "key.jei.toggleOverlay": "Mostrar/Ocultar Overlays de JEI", "key.jei.focusSearch": "Seleccionar barra de bús<PERSON>", "key.jei.previousPage": "Página anterior", "key.jei.nextPage": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "key.jei.toggleBookmarkOverlay": "Mostrar/Ocultar ingredientes favoritos", "jei.key.category.recipe.gui": "JEI (Recetas)", "key.jei.recipeBack": "Receta anterior", "key.jei.previousCategory": "Categoría anterior", "key.jei.nextCategory": "Categoría siguiente", "key.jei.previousRecipePage": "Página de receta anterior", "key.jei.nextRecipePage": "Página de receta siguiente", "key.jei.closeRecipeGui": "Cerrar interfaz de recetas", "jei.key.category.cheat.mode": "JEI (Modo Trampa)", "key.jei.toggleCheatMode": "Alternar modo trampa", "key.jei.cheatOneItem": "Obtener 1 ítem", "key.jei.cheatOneItem2": "Obtener 1 ítem", "key.jei.cheatItemStack": "Obtener 1 stack", "key.jei.cheatItemStack2": "Obtener 1 stack", "jei.key.category.hover.config.button": "JEI (Cursor sobre botón de configuración)", "key.jei.toggleCheatModeConfigButton": "Alternar modo trampa", "jei.key.category.edit.mode": "JEI (Modo Edición)", "key.jei.toggleEditMode": "Alternar modo ocultar ingredientes", "key.jei.toggleHideIngredient": "Ocultar ingrediente", "key.jei.toggleWildcardHideIngredient": "Ocultar ingrediente (comodín)", "jei.key.category.mouse.hover": "JEI (Cursor del mouse)", "key.jei.bookmark": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>ar favorito", "key.jei.showRecipe": "Mostrar receta", "key.jei.showRecipe2": "Mostrar receta", "key.jei.showUses": "Mostrar usos", "key.jei.showUses2": "Mostrar usos", "key.jei.transferRecipeBookmark": "Fabricar receta favorita (una vez)", "key.jei.maxTransferRecipeBookmark": "Fabricar receta favorita (muchas veces)", "jei.key.category.search": "JEI (Filtro de búsqueda)", "key.jei.clearSearchBar": "Limpiar b<PERSON>", "key.jei.previousSearch": "Búsqueda anterior", "key.jei.nextSearch": "Búsqueda siguiente", "jei.key.category.dev.tools": "JEI (Herramientas de desarrollo)", "key.jei.copy.recipe.id": "Copiar ID de receta al portapapeles", "jei.config": "Configuración de JEI", "jei.config.name": "Nombre: %s", "jei.config.description": "Descripción: %s", "jei.config.valueValues": "Valores válidos: %s", "jei.config.defaultValue": "Valor predeterminado: %s", "jei.config.title": "Configuración de %MODNAME", "jei.config.default": "Predeterminado", "jei.config.valid": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.mode": "Modo", "jei.config.mode.description": "Cambiá el modo en el que está funcionando JEI.", "jei.config.mode.cheatItemsEnabled": "<PERSON><PERSON>", "jei.config.mode.cheatItemsEnabled.description": "Otorga los ítems en lugar de mostrar la receta.", "jei.config.mode.editEnabled": "<PERSON><PERSON>", "jei.config.mode.editEnabled.description": "Permití ocultar o mostrar ingredientes haciendo clic en ellos en la lista.", "jei.config.interface": "Interfaz", "jei.config.interface.description": "Opciones relacionadas con la interfaz de usuario.", "jei.config.interface.overlayEnabled": "Mostrar Lista de Ingredientes", "jei.config.interface.overlayEnabled.description": "Muestra la lista de ingredientes junto a las interfaces abiertas.", "jei.config.interface.bookmarkOverlayEnabled": "Mostrar Lista de Favoritos", "jei.config.interface.bookmarkOverlayEnabled.description": "Muestra la lista de favoritos junto a las interfaces abiertas.", "jei.config.client.appearance": "Apariencia", "jei.config.client.appearance.description": "Opciones para cambiar la apariencia de JEI.", "jei.config.client.appearance.centerSearch": "Centrar <PERSON> de Búsqueda", "jei.config.client.appearance.centerSearch.description": "Mueve la barra de búsqueda de JEI al centro inferior de la pantalla.", "jei.config.client.appearance.recipeGuiHeight": "Altura de la GUI de Recetas", "jei.config.client.appearance.recipeGuiHeight.description": "Altura máxima de la interfaz de recetas (en píxeles).", "jei.config.client.cheating": "Trampas", "jei.config.client.cheating.description": "Opciones relacionadas con el uso de trampas.", "jei.config.client.cheating.giveMode": "<PERSON><PERSON>ga", "jei.config.client.cheating.giveMode.description": "Elegí si JEI debería darte los ingredientes directo al inventario o levantarlos con el mouse.", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled": "Trampear a la Hotbar con Atajos", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled.description": "Habilita que se puedan poner ítems en la hotbar usando Shift + números.", "jei.config.client.cheating.showHiddenIngredients": "Mostrar Ingredientes Ocultos", "jei.config.client.cheating.showHiddenIngredients.description": "Muestra ingredientes que no están en el menú creativo.", "jei.config.client.cheating.showTagRecipesEnabled": "Mostrar Recetas por Tags", "jei.config.client.cheating.showTagRecipesEnabled.description": "Muestra recetas relacionadas a etiquetas de ítems o bloques.", "jei.config.client.bookmarks": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.client.bookmarks.description": "Opciones relacionadas con el guardado de ingredientes y recetas como favoritos.", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "Agregar Favoritos al Principio", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "Si está activado, los nuevos favoritos se agregan al inicio. Si no, al final.", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled": "Arrastrar para Reordenar Favoritos", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled.description": "Permite reordenar los favoritos arrastrándolos.", "jei.config.client.tooltips": "Tooltips", "jei.config.client.tooltips.description": "Opciones relacionadas con los tooltips en JEI.", "jei.config.client.tooltips.bookmarkTooltipFeatures": "Funciones de Tooltips en Favoritos", "jei.config.client.tooltips.bookmarkTooltipFeatures.description": "Funciones adicionales en los tooltips de favoritos.", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures": "Shift para Tooltips de Favoritos", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures.description": "Mantené Shift para ver funciones extra en los tooltips de favoritos.", "jei.config.client.tooltips.showCreativeTabNamesEnabled": "Mostrar Nombre de la Pestaña Creativa", "jei.config.client.tooltips.showCreativeTabNamesEnabled.description": "Muestra el nombre de la pestaña creativa en los tooltips de ingredientes.", "jei.config.client.tooltips.tagContentTooltipEnabled": "Mostrar Contenido de Etiquetas", "jei.config.client.tooltips.tagContentTooltipEnabled.description": "Muestra el contenido de etiquetas en los tooltips al ver ingredientes de recetas.", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled": "Ocultar Etiquetas de un Solo Ingrediente", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled.description": "Oculta el contenido de etiquetas cuando solo tienen un ingrediente.", "jei.config.client.lookups": "Búsquedas", "jei.config.client.lookups.description": "Opciones relacionadas con la búsqueda de usos y recetas de ingredientes en JEI.", "jei.config.client.lookups.lookupFluidContentsEnabled": "Buscar Contenido de Fluidos", "jei.config.client.lookups.lookupFluidContentsEnabled.description": "<PERSON><PERSON>do buscás recetas con ítems que contienen fluidos, tamb<PERSON><PERSON> buscá recetas para los fluidos.", "jei.config.client.lookups.lookupBlockTagsEnabled": "Buscar Etiquetas de Ítems/Bloques", "jei.config.client.lookups.lookupBlockTagsEnabled.description": "Al buscar etiquetas de ítems, también se incluyen las etiquetas de bloques por defecto contenidas en ellos.", "jei.config.client.input": "Entrada", "jei.config.client.input.description": "Opciones relacionadas con las entradas de usuario en JEI.", "jei.config.client.input.dragDelayInMilliseconds": "<PERSON><PERSON><PERSON>", "jei.config.client.input.dragDelayInMilliseconds.description": "Cantidad de milisegundos antes de que un clic largo se considere como arrastre.", "jei.config.client.input.smoothScrollRate": "Velocidad de Desplazamiento Suave", "jei.config.client.input.smoothScrollRate.description": "Velocidad de desplazamiento del mouse en cajas con desplazamiento suave. Medido en píxeles.", "jei.config.client.performance": "Rendimiento", "jei.config.client.performance.description": "Opciones relacionadas con la optimización del rendimiento en JEI.", "jei.config.client.performance.lowMemorySlowSearchEnabled": "Búsqueda de Bajo Consumo", "jei.config.client.performance.lowMemorySlowSearchEnabled.description": "Activa el modo de búsqueda de bajo consumo (usa menos RAM pero es más lento).", "jei.config.client.advanced": "<PERSON><PERSON><PERSON>", "jei.config.client.advanced.description": "Opciones avanzadas para modificar el funcionamiento de JEI.", "jei.config.client.advanced.catchRenderErrorsEnabled": "<PERSON><PERSON>r <PERSON>rro<PERSON> de Renderizado", "jei.config.client.advanced.catchRenderErrorsEnabled.description": "Captura errores de renderizado de ingredientes con mods y trata de recuperarse en lugar de crashear.", "jei.config.client.sorting": "Ordenamiento", "jei.config.client.sorting.description": "Opciones relacionadas con cómo JEI ordena recetas e ingredientes.", "jei.config.client.sorting.ingredientSortStages": "Etapas de Ordenamiento de Ingredientes", "jei.config.client.sorting.ingredientSortStages.description": "Orden de clasificación para la lista de ingredientes.", "jei.config.client.sorting.recipeSorterStages": "Etapas de Ordenamiento de Recetas", "jei.config.client.sorting.recipeSorterStages.description": "Orden de clasificación para las recetas mostradas.", "jei.config.client.search": "Búsqueda", "jei.config.client.search.description": "Opciones relacionadas con cómo JEI busca recetas.", "jei.config.client.search.modNameSearchMode": "Modo de Búsqueda por @Mod", "jei.config.client.search.modNameSearchMode.description": "Modo de búsqueda para nombres de mods (prefijo: @).", "jei.config.client.search.tagSearchMode": "Modo de Búsqueda por #Etiqueta", "jei.config.client.search.tagSearchMode.description": "Modo de búsqueda para etiquetas (prefijo: #).", "jei.config.client.search.tooltipSearchMode": "Modo de Búsqueda por $Tooltip", "jei.config.client.search.tooltipSearchMode.description": "Modo de búsqueda por descripciones emergentes (prefijo: $).", "jei.config.client.search.colorSearchMode": "^Modo de Búsqueda por Color", "jei.config.client.search.colorSearchMode.description": "Modo de búsqueda por colores (prefijo: ^).", "jei.config.client.search.resourceLocationSearchMode": "&Modo de Búsqueda por Ubicación de Recurso", "jei.config.client.search.resourceLocationSearchMode.description": "Modo de búsqueda por ubicaciones de recursos (prefijo: &).", "jei.config.client.search.creativeTabSearchMode": "%Modo de Búsqueda por Pestaña Creativa", "jei.config.client.search.creativeTabSearchMode.description": "Modo de búsqueda por nombre de pestañas del modo creativo (prefijo: %).", "jei.config.client.search.searchAdvancedTooltips": "Buscar en Tooltips Avanzados", "jei.config.client.search.searchAdvancedTooltips.description": "Buscar en tooltips avanzados (visibles con F3 + H).", "jei.config.client.search.searchModIds": "Buscar ID de Mod", "jei.config.client.search.searchModIds.description": "Buscar IDs de mods además de los nombres.", "jei.config.client.search.searchModAliases": "Buscar Alias de Mod", "jei.config.client.search.searchModAliases.description": "<PERSON><PERSON> alias (nombres alternativos) de mods agregados por plugins, adem<PERSON> de los nombres.", "jei.config.client.search.searchShortModNames": "Buscar Nombres Cortos de Mods", "jei.config.client.search.searchShortModNames.description": "Buscar por siglas o letras iniciales del nombre de un mod.", "jei.config.client.search.searchIngredientAliases": "Buscar Alias de Ingredientes", "jei.config.client.search.searchIngredientAliases.description": "<PERSON><PERSON> alias (nombres alternativos) de ingredientes agregados por plugins, además de los nombres de ingredientes.", "jei.config.client.ingredientList": "Lista de Ingredientes", "jei.config.client.ingredientList.description": "Opciones relacionadas con la Lista de Ingredientes (la lista que aparece a la derecha de la pantalla).", "jei.config.client.ingredientList.maxRows": "<PERSON><PERSON>", "jei.config.client.ingredientList.maxRows.description": "Cantidad máxima de filas visibles.", "jei.config.client.ingredientList.maxColumns": "Columnas M<PERSON>", "jei.config.client.ingredientList.maxColumns.description": "Cantidad máxima de columnas visibles.", "jei.config.client.ingredientList.horizontalAlignment": "Alineación Horizontal", "jei.config.client.ingredientList.horizontalAlignment.description": "Alineación horizontal de la lista de ingredientes dentro del área disponible.", "jei.config.client.ingredientList.verticalAlignment": "Alineación Vertical", "jei.config.client.ingredientList.verticalAlignment.description": "Alineación vertical de la lista de ingredientes dentro del área disponible.", "jei.config.client.ingredientList.buttonNavigationVisibility": "Visibilidad de Navegación", "jei.config.client.ingredientList.buttonNavigationVisibility.description": "Visibilidad de los botones superiores. Usá AUTO_HIDE para que se muestren solo si hay varias páginas.", "jei.config.client.ingredientList.drawBackground": "Mostrar Fondo de Interfaz", "jei.config.client.ingredientList.drawBackground.description": "Activá esto para mostrar una textura de fondo detrás de la lista de ingredientes.", "jei.config.client.bookmarkList": "Lista de Favoritos", "jei.config.client.bookmarkList.description": "Opciones relacionadas con la Lista de Favoritos (la lista de ingredientes favoritos a la izquierda de la pantalla).", "jei.config.client.bookmarkList.maxRows": "<PERSON><PERSON>", "jei.config.client.bookmarkList.maxRows.description": "Cantidad máxima de filas visibles.", "jei.config.client.bookmarkList.maxColumns": "Columnas M<PERSON>", "jei.config.client.bookmarkList.maxColumns.description": "Cantidad máxima de columnas visibles.", "jei.config.client.bookmarkList.horizontalAlignment": "Alineación Horizontal", "jei.config.client.bookmarkList.horizontalAlignment.description": "Alineación horizontal de la lista de favoritos dentro del área disponible.", "jei.config.client.bookmarkList.verticalAlignment": "Alineación Vertical", "jei.config.client.bookmarkList.verticalAlignment.description": "Alineación vertical de la lista de favoritos dentro del área disponible.", "jei.config.client.bookmarkList.buttonNavigationVisibility": "Visibilidad de Navegación", "jei.config.client.bookmarkList.buttonNavigationVisibility.description": "Visibilidad de los botones superiores. Usá AUTO_HIDE para que se muestren solo si hay varias páginas.", "jei.config.client.bookmarkList.drawBackground": "Mostrar Fondo de Interfaz", "jei.config.client.bookmarkList.drawBackground.description": "Activá esto para mostrar una textura de fondo detrás de la lista de favoritos.", "jei.config.client.advanced.itemBlacklist": "Lista Negra de Ingredientes", "jei.config.client.advanced.itemBlacklist.description": "Lista de ingredientes que no deberían mostrarse en la superposición de la lista de ingredientes. Formato: modId[:name[:meta]]. El modo ocultar ingredientes va a agregar o quitar entradas automáticamente.", "jei.config.client.advanced.maxColumns": "<PERSON><PERSON> de Superposición", "jei.config.client.advanced.maxColumns.description": "El ancho máximo para las superposiciones de listas de ingredientes y favoritos.", "jei.config.modIdFormat.modName": "Nombre del Mod", "jei.config.modIdFormat.modName.description": "Opciones de configuración para mostrar los nombres de los mods.", "jei.config.modIdFormat.modName.modNameFormat": "Formato del Nombre del Mod", "jei.config.modIdFormat.modName.modNameFormat.description": "Formato del nombre del mod en los tooltips de las GUIs de JEI. Dejalo vacío para desactivarlo.", "jei.config.debug.debug": "Depuración", "jei.config.debug.debug.description": "Opciones para ayudar a desarrolladores a depurar errores en JEI.", "jei.config.debug.debug.debugMode": "Modo de Depuración", "jei.config.debug.debug.debugMode.description": "Activar modo de depuración.", "jei.config.debug.debug.debugGuis": "GUIs de Depuración", "jei.config.debug.debug.debugGuis.description": "Activar modo GUIs de depuración.", "jei.config.debug.debug.debugInputs": "Entradas de Depuración", "jei.config.debug.debug.debugInputs.description": "Activar modo de entradas de depuración.", "jei.config.debug.debug.debugInfoTooltipsEnabled": "Tooltips de Info de Depuración", "jei.config.debug.debug.debugInfoTooltipsEnabled.description": "Agregar información de depuración a los tooltips de ingredientes cuando están activados los tooltips avanzados.", "jei.config.debug.debug.crashingTestItemsEnabled": "Habilitar Ítems de Prueba que <PERSON>", "jei.config.debug.debug.crashingTestItemsEnabled.description": "Agrega ingredientes a JEI que causan crash intencionalmente, para ayudar con la depuración.", "jei.config.debug.debug.logSuffixTreeStats": "Registrar Estadísticas de Árboles de Sufijos", "jei.config.debug.debug.logSuffixTreeStats.description": "Registrar información sobre los árboles de sufijos usados para las búsquedas, para ayudar en la depuración.", "jei.config.colors.colors": "Colores", "jei.config.colors.colors.description": "Opciones relacionadas con la búsqueda por color de ítems en JEI.", "jei.config.colors.colors.searchColors": "Buscar por Color", "jei.config.colors.colors.searchColors.description": "Valores de color a buscar.", "gui.jei.editMode.description": "Modo de Ocultar Ingredientes de JEI:", "gui.jei.editMode.description.hide": "Presioná \"%s\" para ocultar.", "gui.jei.editMode.description.hide.wild": "Presioná \"%s\" para ocultar con comodín.", "gui.jei.category.craftingTable": "Crafteo", "gui.jei.category.stoneCutter": "Corte de Piedra", "gui.jei.category.smelting": "<PERSON><PERSON>", "gui.jei.category.smoking": "<PERSON><PERSON><PERSON>", "gui.jei.category.blasting": "Alto Horno", "gui.jei.category.campfire": "Cocina al Fuego", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.smelting_fuel": "Combustible de Fundición", "gui.jei.category.smoking_fuel": "Combustible <PERSON>do", "gui.jei.category.blasting_fuel": "Combustible de Alto Horno", "gui.jei.category.fuel.smeltCount.single": "Quema 1 ítem", "gui.jei.category.fuel.smeltCount": "Quema %s ítems", "gui.jei.category.brewing": "Pociones", "gui.jei.category.brewing.steps": "Pasos: %s", "gui.jei.category.compostable": "Compostaje", "gui.jei.category.compostable.chance": "Probabilidad: %s%%", "gui.jei.category.grindstone": "<PERSON><PERSON>", "gui.jei.category.grindstone.experience": "%s a %s de XP", "gui.jei.category.itemInformation": "Información", "gui.jei.category.tagInformation": "Etiquetas: %s", "gui.jei.category.tagInformation.block": "Etiquetas de Bloques", "gui.jei.category.tagInformation.fluid": "Etiquetas de Fluidos", "gui.jei.category.tagInformation.item": "Etiquetas de Ítems", "gui.jei.category.recipe.crashed": "Esta receta falló. Consultá los registros del cliente para más detalles.", "jei.message.configured": "Instalá el mod \"Configured\" para acceder a la configuración en el juego", "jei.message.config.folder": "O hacé clic acá para abrir la carpeta de configuración de JEI", "jei.message.copy.recipe.id.success": "El siguiente ID de receta se copió al portapapeles: %s", "jei.message.copy.recipe.id.failure": "No se pudo copiar el ID de receta al portapapeles porque es desconocido", "jei.message.missing.recipes.from.server": "A JEI le faltan recetas. Por favor, instalá JEI en el servidor para sincronizar las recetas con el cliente.\nDesde Minecraft 1.21.2, las recetas se guardan en el servidor y no en el cliente.", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "jei.key.shift": "SHIFT", "jei.key.mouse.left": "CLIC IZQUIERDO", "jei.key.mouse.right": "CLIC DERECHO", "description.jei.wooden.door.1": "Las puertas de madera sirven para que los monstruos no entren a tu construcción.\\nFrases de prueba.", "description.jei.wooden.door.2": "Hacer clic sobre una puerta cambia su estado entre abierta y cerrada.", "description.jei.wooden.door.3": "Las puertas de madera se pueden abrir/cerrar con circuitos de redstone.", "description.jei.debug.formatting.1": "Prueba de reemplazo de formato %s.", "description.jei.debug.formatting.2": "Prueba de reemplazo de formato %s %s.", "description.jei.debug.formatting.3": "%s anidado", "jei.alias.panda.spawn.egg": "en peligro", "jei.alias.villager.spawn.egg": "HMMM"}