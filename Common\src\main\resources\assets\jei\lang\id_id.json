{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Konfigurasi JEI", "jei.tooltip.show.recipes": "<PERSON><PERSON><PERSON><PERSON>", "jei.tooltip.delete.item": "Klik untuk Menghapus", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "Pindahkan Bend<PERSON>", "jei.tooltip.recipe.tag": "Menerima apapun: %s", "jei.tooltip.item.colors": "Warna: %s", "jei.tooltip.shapeless.recipe": "Resep <PERSON>uk", "jei.tooltip.cheat.mode.button.enabled": "Mode Curang diaktifkan", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Tekan %s untuk mengaktifkannya.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s di sini untuk mengaktifkannya.", "jei.tooltip.recipe.by": "Rese<PERSON>: %s", "jei.tooltip.recipe.id": "ID Resep: %s", "jei.tooltip.not.enough.space": "Tidak cukup ruang untuk menampilkan JEI di sini.", "jei.tooltip.ingredient.list.disabled": "Overlay JEI dinonaktifkan.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Tekan %s untuk mengaktifkannya.", "jei.tooltip.bookmarks": "Penanda JEI", "jei.tooltip.bookmarks.usage.nokey": "Tambahkan pengikatan kunci untuk penanda JEI di pengaturan Kontrol Anda.", "jei.tooltip.bookmarks.usage.key": "<PERSON><PERSON><PERSON> kursor ke atas benda dan tekan \"%s\" untuk menandai-nya.", "jei.tooltip.bookmarks.not.enough.space": "Tidak cukup ruang untuk menampilkan penanda di sini.", "jei.tooltip.error.recipe.transfer.missing": "<PERSON><PERSON> yang <PERSON>", "jei.tooltip.error.recipe.transfer.inventory.full": "<PERSON><PERSON><PERSON><PERSON> terlalu penuh", "jei.tooltip.error.recipe.transfer.no.server": "Peladen harus menginstall JEI", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Resep terlalu besar untuk dibuat di kisi kerajinan pemain 2x2.", "jei.tooltip.error.crash": "Kesalahan tooltip, lihat log", "jei.chat.error.no.cheat.permission.1": "Anda tidak memiliki izin untuk menggunakan Mode Curang JEI.", "key.jei.toggleOverlay": "Tampilkan/Sembunyikan JEI", "key.jei.focusSearch": "<PERSON><PERSON><PERSON>", "key.jei.previousPage": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.nextPage": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.toggleBookmarkOverlay": "<PERSON><PERSON><PERSON><PERSON>/Sembunyikan <PERSON>", "key.jei.recipeBack": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "key.jei.previousCategory": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.nextCategory": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.toggleCheatMode": "Alihkan Mode Curang", "key.jei.toggleEditMode": "Alihkan Mode Sembunyikan/Edit", "key.jei.bookmark": "Tambah/Hapus Penanda", "key.jei.showRecipe": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.showRecipe2": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.showUses": "<PERSON><PERSON><PERSON><PERSON>", "key.jei.showUses2": "<PERSON><PERSON><PERSON><PERSON>", "jei.config": "Konfigurasi JEI", "jei.config.default": "<PERSON><PERSON><PERSON>", "jei.config.valid": "Sah", "jei.config.title": "Konfigurasi %MODNAME", "jei.config.mode": "Mode", "jei.config.mode.description": "Ubah mode pengoperasian JEI.", "jei.config.mode.cheatItemsEnabled": "Mode Curang", "jei.config.mode.cheatItemsEnabled.description": "Berikan benda alih-alih menu<PERSON>n resepnya.", "jei.config.mode.editEnabled": "Mode Sembunyikan Benda", "jei.config.mode.editEnabled.description": "Klik untuk menampilkan/menyembunyikan elemen dalam daftar benda.", "jei.config.interface": "Antarmuka", "jei.config.interface.description": "Opsi terkait Antarmuka Pengguna.", "jei.config.interface.overlayEnabled": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.interface.overlayEnabled.description": "Tampilkan daftar benda di sebelah GUI yang terbuka.", "jei.config.interface.bookmarkOverlayEnabled": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.interface.bookmarkOverlayEnabled.description": "<PERSON><PERSON><PERSON>an daftar penanda di samping GUI yang terbuka.", "jei.config.client.search": "<PERSON><PERSON>", "jei.config.client.search.description": "Opsi yang berkaitan dengan bilah pencarian.", "jei.config.client.search.modNameSearchMode": "@ModName", "jei.config.client.search.modNameSearchMode.description": "<PERSON><PERSON><PERSON> (awalan: @)", "jei.config.client.search.tooltipSearchMode": "$Tooltip", "jei.config.client.search.tooltipSearchMode.description": "<PERSON><PERSON><PERSON> (awalan: $)", "jei.config.client.search.tagSearchMode": "#Tag", "jei.config.client.search.tagSearchMode.description": "<PERSON><PERSON><PERSON> (awalan: #)", "jei.config.client.search.creativeTabSearchMode": "%%CreativeModeTab", "jei.config.client.search.creativeTabSearchMode.description": "<PERSON><PERSON><PERSON> (awalan: %)", "jei.config.client.search.colorSearchMode": "^Color", "jei.config.client.search.colorSearchMode.description": "<PERSON><PERSON><PERSON> (awalan: ^)", "jei.config.client.search.resourceLocationSearchMode": "&ResourceLocation", "jei.config.client.search.resourceLocationSearchMode.description": "Pencarian ID Sumber <PERSON>a (awalan: &)", "jei.config.client.search.searchAdvancedTooltips": "Cari tooltips lanjutan", "jei.config.client.advanced": "Lanjutan", "jei.config.client.advanced.description": "Opsi konfigurasi lanjutan untuk mengubah cara JEI berfungsi.", "jei.config.client.advanced.itemBlacklist": "<PERSON><PERSON><PERSON>", "jei.config.client.advanced.itemBlacklist.description": "Daftar benda yang tidak boleh ditampilkan dalam daftar. Format: modId[:name[:meta]]. Mode Sembunyikan Benda akan secara otomatis menambah atau menghapus entri di sini.", "jei.config.debug.debug.debugMode": "Mode Debug", "jei.config.debug.debug.debugMode.description": "<PERSON><PERSON> berguna untuk pengembang JEI, menambahkan benda uji coba dan beberapa resep debug.", "jei.config.client.appearance.centerSearch": "<PERSON><PERSON><PERSON>", "jei.config.client.appearance.centerSearch.description": "Pindahkan bilah pencarian JEI ke tengah bawah layar.", "jei.config.modIdFormat.modName.modNameFormat": "Format Nama Mod", "jei.config.modIdFormat.modName.modNameFormat.description": "Bagaimana nama mod harus diformat di tooltip untuk GUI JEI. Biarkan kosong untuk menonaktifkan.", "jei.config.client.advanced.maxColumns": "<PERSON><PERSON>", "jei.config.client.advanced.maxColumns.description": "<PERSON><PERSON> maksimum daftar benda.", "jei.config.client.appearance.recipeGuiHeight": "Tinggi Maksimum GUI Resep", "jei.config.client.appearance.recipeGuiHeight.description": "Ketinggian maksimum GUI resep.", "jei.config.client.cheating.giveMode": "Mode Pemberian", "jei.config.client.cheating.giveMode.description": "<PERSON><PERSON><PERSON> a<PERSON>h JEI harus memberikan benda langsung ke persediaan atau mengambilnya dengan mouse.", "gui.jei.editMode.description": "Mode Sembunyikan Benda JEI:", "gui.jei.category.craftingTable": "<PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.stoneCutter": "<PERSON><PERSON><PERSON>", "gui.jei.category.smelting": "<PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.smoking": "Pengasap", "gui.jei.category.blasting": "<PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.campfire": "<PERSON><PERSON>", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.fuel": "<PERSON><PERSON> bakar", "gui.jei.category.fuel.smeltCount.single": "Melebur 1 benda", "gui.jei.category.fuel.smeltCount": "Melebur %s benda", "gui.jei.category.brewing": "Alat Peramu", "gui.jei.category.brewing.steps": "Langkah: %s", "gui.jei.category.itemInformation": "Informasi", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}