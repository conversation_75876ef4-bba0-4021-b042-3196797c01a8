#colors
public net.minecraft.client.renderer.texture.TextureAtlasSprite mainImage

#RecipeBookGui
public net.minecraft.client.gui.screens.recipebook.RecipeBookComponent width
public net.minecraft.client.gui.screens.recipebook.RecipeBookComponent height
public net.minecraft.client.gui.screens.recipebook.RecipeBookComponent xOffset
public net.minecraft.client.gui.screens.recipebook.RecipeBookComponent tabButtons
public net.minecraft.client.gui.screens.inventory.AbstractRecipeBookScreen recipeBookComponent

# AbstractWidget
public net.minecraft.client.gui.components.AbstractWidget setFocused(Z)V

# EditBox
public net.minecraft.client.gui.components.EditBox canLoseFocus

#potions
public net.minecraft.world.item.alchemy.PotionBrewing containers
public net.minecraft.world.item.alchemy.PotionBrewing containerMixes
public net.minecraft.world.item.alchemy.PotionBrewing potionMixes
public net.minecraft.world.item.alchemy.PotionBrewing$Mix

# Recipes
public net.minecraft.world.item.crafting.ShapedRecipePattern shrink(Ljava/util/List;)[Ljava/lang/String;

# SmithingRecipe
public net.minecraft.world.item.crafting.SmithingTransformRecipe base
public net.minecraft.world.item.crafting.SmithingTransformRecipe addition
public net.minecraft.world.item.crafting.SmithingTransformRecipe template
public net.minecraft.world.item.crafting.SmithingTrimRecipe base
public net.minecraft.world.item.crafting.SmithingTrimRecipe addition
public net.minecraft.world.item.crafting.SmithingTrimRecipe template

# TagsUpdatedEvent is fired very early, these are the places where the connection may be stored at that time
public net.minecraft.client.Minecraft pendingConnection
public net.minecraft.client.gui.screens.ConnectScreen connection

# Toasts
public net.minecraft.client.gui.components.toasts.ToastManager$ToastInstance
public net.minecraft.client.gui.components.toasts.ToastManager visibleToasts

# Rendering
public net.minecraft.client.gui.GuiGraphics blitSprite(Lcom/mojang/blaze3d/pipeline/RenderPipeline;Lnet/minecraft/client/renderer/texture/TextureAtlasSprite;IIIIIIIII)V
public net.minecraft.client.gui.GuiGraphics blitNineSlicedSprite(Lcom/mojang/blaze3d/pipeline/RenderPipeline;Lnet/minecraft/client/renderer/texture/TextureAtlasSprite;Lnet/minecraft/client/resources/metadata/gui/GuiSpriteScaling$NineSlice;IIIII)V
public net.minecraft.client.gui.GuiGraphics blitTiledSprite(Lcom/mojang/blaze3d/pipeline/RenderPipeline;Lnet/minecraft/client/renderer/texture/TextureAtlasSprite;IIIIIIIIIII)V

public net.minecraft.client.gui.screens.recipebook.RecipeBookComponent getXOrigin()I
public net.minecraft.client.gui.screens.recipebook.RecipeBookComponent getYOrigin()I
