{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Configurer JEI", "jei.tooltip.show.recipes": "Afficher les recettes", "jei.tooltip.delete.item": "Cliquer pour supprimer", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "Transférer les objets", "jei.tooltip.recipe.tag": "Accepte n'importe lequel : %s", "jei.tooltip.item.colors": "Couleurs : %s", "jei.tooltip.shapeless.recipe": "Recette sans forme", "jei.tooltip.cheat.mode.button.enabled": "Mode triche", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Pressez %s pour l'activer.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s ici pour le désactiver.", "jei.tooltip.recipe.by": "Recette par : %s", "jei.tooltip.recipe.id": "ID de la recette : %s", "jei.tooltip.not.enough.space": "Il n'y a pas assez de place pour afficher la liste d'ingrédients.", "jei.tooltip.ingredient.list.disabled": "L'overlay JEI a été désactivé.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Pressez %s pour l'activer.", "jei.tooltip.bookmarks": "Marque-pages JEI", "jei.tooltip.bookmarks.usage.nokey": "A<PERSON><PERSON>z un raccourci pour les marque-page JEI dans vos paramètres de raccourcis.", "jei.tooltip.bookmarks.usage.key": "Survolez un objet avec votre souris et pressez \"%s\" pour l'épingler.", "jei.tooltip.bookmarks.not.enough.space": "Il n'y a pas assez de place pour afficher les marque-pages ici.", "jei.tooltip.error.recipe.transfer.missing": "Objets manquants", "jei.tooltip.error.recipe.transfer.inventory.full": "Inventaire plein", "jei.tooltip.error.recipe.transfer.no.server": "JEI doit aussi être installé sur le serveur", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Cette recette est trop grande pour être fabriquée dans la grille 2x2 du joueur.", "jei.tooltip.error.crash": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON><PERSON><PERSON>, consulter le log", "jei.chat.error.no.cheat.permission.1": "Vous n'avez pas l'autorisation d'utiliser le mode triche de JEI.", "jei.chat.error.no.cheat.permission.disabled": "Sur ce serveur, le mode triche de JEI est désactivé pour tous les joueurs.", "jei.chat.error.no.cheat.permission.enabled": "Sur ce serveur, les types de joueurs suivants peuvent utiliser le mode triche de JEI:", "jei.chat.error.no.cheat.permission.creative": "les joueurs qui sont en mode créatif", "jei.chat.error.no.cheat.permission.op": "les joueurs qui ont le statut d'opérateur (/op)", "jei.chat.error.no.cheat.permission.give": "les joueurs qui peuvent utiliser /give", "jei.key.category.overlays": "JEI (Overlays)", "key.jei.toggleOverlay": "Afficher/cacher JEI", "key.jei.focusSearch": "Sélectionner la barre de recherche", "key.jei.previousPage": "Afficher la page précédente", "key.jei.nextPage": "Afficher la page suivante", "key.jei.toggleBookmarkOverlay": "Afficher/cacher les objets épinglés", "jei.key.category.recipe.gui": "JEI (Recipes)", "key.jei.recipeBack": "<PERSON><PERSON> la recette précédente", "key.jei.previousCategory": "Afficher la recette précédente", "key.jei.nextCategory": "Affiàcher la prochaine catégorie de recette", "key.jei.previousRecipePage": "Page précédente de la recette", "key.jei.nextRecipePage": "Page suivante de la recette", "key.jei.closeRecipeGui": "Fermer l'interface utilisateur des recettes", "jei.key.category.cheat.mode": "JEI (Cheat Mode)", "key.jei.toggleCheatMode": "Activer/dés<PERSON>r le Mode triche", "key.jei.cheatOneItem": "Triche 1 item", "key.jei.cheatOneItem2": "Triche 1 item", "key.jei.cheatItemStack": "Triche 1 stack", "key.jei.cheatItemStack2": "Triche 1 stack", "jei.key.category.hover.config.button": "JEI (Hovering with <PERSON> over Config <PERSON>)", "key.jei.toggleCheatModeConfigButton": "Basculer en mode triche", "jei.key.category.edit.mode": "JEI (Edit Mode)", "key.jei.toggleEditMode": "Basculer en mode Masquer/Modifier", "key.jei.toggleHideIngredient": "Masquer l'ingrédient", "key.jei.toggleWildcardHideIngredient": "Masquer l'ingrédient (joker)", "jei.key.category.mouse.hover": "JEI (Hovering with Mouse)", "key.jei.bookmark": "Ajouter/supprimer un objet é<PERSON>", "key.jei.showRecipe": "Afficher la recette", "key.jei.showRecipe2": "Afficher la recette", "key.jei.showUses": "Montrer les utilisations", "key.jei.showUses2": "Montrer les utilisations", "jei.key.category.search": "JEI (Search Filter)", "key.jei.clearSearchBar": "Eff<PERSON><PERSON> le filtre de recherche", "key.jei.previousSearch": "Recherche précédente", "key.jei.nextSearch": "Recherche suivante", "jei.key.category.dev.tools": "JEI (Dev Tools)", "key.jei.copy.recipe.id": "Copier l'ID de la recette dans le presse-papiers", "jei.config": "Configurer JEI", "jei.config.default": "Défaut", "jei.config.valid": "Valide", "jei.config.title": "Configuration de %MODNAME", "jei.config.mode": "Mode", "jei.config.mode.description": "Change le mode dans lequel JEI fonctionne.", "jei.config.mode.cheatItemsEnabled": "Mode triche activé", "jei.config.mode.cheatItemsEnabled.description": "<PERSON><PERSON> les objets au lieu de montrer la recette.", "jei.config.mode.editEnabled": "Mode masquage d'objets", "jei.config.mode.editEnabled.description": "Masque/affiche les objets en les cliquant dans la liste.", "jei.config.interface": "Interface", "jei.config.interface.description": "Options relatives à l'interface utilisateur.", "jei.config.interface.overlayEnabled": "Liste d'objets activée", "jei.config.interface.overlayEnabled.description": "Afficher la liste des objets à côté de l'interface utilisateur ouverte.", "jei.config.interface.bookmarkOverlayEnabled": "Afficher les marque-pages", "jei.config.interface.bookmarkOverlayEnabled.description": "Afficher la liste des marque-pages à côté de l'interface d'utilisateur ouverte.", "jei.config.client.search": "Options de recherche", "jei.config.client.search.description": "Options relatives à la barre de recherche.", "jei.config.client.search.modNameSearchMode": "@NomDuMod", "jei.config.client.search.modNameSearchMode.description": "Recherche par nom de mods (préfixe : @)", "jei.config.client.search.tooltipSearchMode": "$Infobulle", "jei.config.client.search.tooltipSearchMode.description": "Recherche par infobulles (préfixe : $)", "jei.config.client.search.tagSearchMode": "#Tag", "jei.config.client.search.tagSearchMode.description": "Mode de recherche par tag (prefix: #)", "jei.config.client.search.creativeTabSearchMode": "%%MenuCréatif", "jei.config.client.search.creativeTabSearchMode.description": "Recherche par nom du Menu Créatif (préfixe : %)", "jei.config.client.search.colorSearchMode": "^Couleur", "jei.config.client.search.colorSearchMode.description": "Recherche par couleur d'objet (préfixe : ^)", "jei.config.client.search.resourceLocationSearchMode": "&IDdeRessource", "jei.config.client.search.resourceLocationSearchMode.description": "Recherche par ID de ressource (préfixe : &)", "jei.config.client.search.searchAdvancedTooltips": "Recherche avancée par infobulle", "jei.config.client.advanced": "<PERSON><PERSON><PERSON>", "jei.config.client.advanced.description": "Options de configuration avancées pour modifier le fonctionnement de JEI.", "jei.config.client.advanced.itemBlacklist": "Objets blacklistés", "jei.config.client.advanced.itemBlacklist.description": "Liste des objets qui ne doivent pas être affichés dans la liste. Format : modId[:name[:meta]]. Le mode Edition ajoute/retire automatiquement les objets de la liste.", "jei.config.debug.debug.debugMode": "Mode de débogage", "jei.config.debug.debug.debugMode.description": "Utile seulement pour les développeurs de JEI, ajoute des milliers d'objets et de recettes factices.", "jei.config.client.appearance.centerSearch": "<PERSON>r la barre de recherche", "jei.config.client.appearance.centerSearch.description": "Déplace la barre de recherche de JEI en bas et au centre de l'écran.", "jei.config.modIdFormat.modName.modNameFormat": "Format du nom des mods", "jei.config.modIdFormat.modName.modNameFormat.description": "Comment le nom des mods doit être formaté dans l'infobulle de l'interface JEI. Laisser vide pour désactiver.", "jei.config.client.advanced.maxColumns": "Largeur maximum", "jei.config.client.advanced.maxColumns.description": "Largeur maximum de la liste d'objets.", "jei.config.client.appearance.recipeGuiHeight": "Hauteur maximum", "jei.config.client.appearance.recipeGuiHeight.description": "Hauteur maximum de l'interface de recette.", "jei.config.client.cheating.giveMode": "<PERSON>", "jei.config.client.cheating.giveMode.description": "Choisit si JEI doit placer les objets directement dans l'inventaire ou les accrocher au curseur de la souris.", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "Ajouter de nouveaux marque-pages", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "Si elle est vraie, elle ajoute les nouveaux marque-pages au début de la liste des marque-pages. Si elle est fausse, elle ajoute les nouveaux marque-pages à la fin de la liste des marque-pages.", "gui.jei.editMode.description": "JEI Mode édition de la liste d'objets :", "gui.jei.editMode.description.hide": "<PERSON><PERSON> (%s).", "gui.jei.editMode.description.hide.wild": "Cacher par caractères génériques (%s).", "gui.jei.category.craftingTable": "<PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.stoneCutter": "<PERSON><PERSON><PERSON> de <PERSON>", "gui.jei.category.smelting": "Fourneau", "gui.jei.category.smoking": "Fumoir", "gui.jei.category.blasting": "<PERSON><PERSON> four<PERSON>", "gui.jei.category.campfire": "<PERSON><PERSON>", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.fuel": "Carburant", "gui.jei.category.fuel.smeltCount.single": "Fond 1 objet", "gui.jei.category.fuel.smeltCount": "Fond %s objets", "gui.jei.category.brewing": "Création de potion", "gui.jei.category.brewing.steps": "Étapes: %s", "gui.jei.category.compostable": "Compostable", "gui.jei.category.compostable.chance": "Chance: %s%%", "gui.jei.category.itemInformation": "Information", "jei.message.configured": "Installez le mod \"Configured\" pour accéder à la configuration du jeu.", "jei.message.config.folder": "Cliquez ici pour ouvrir le dossier de configuration de la JEI", "jei.message.copy.recipe.id.success": "Copie de l'ID de la recette dans le presse-papiers: %s", "jei.message.copy.recipe.id.failure": "Échec de la copie de l'ID de la recette dans le presse-papiers, l'ID de la recette est inconnu.", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}