{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Конфигурация JEI", "jei.tooltip.show.recipes": "Отобразить рецепты", "jei.tooltip.delete.item": "Нажми, чтобы удалить.", "jei.tooltip.liquid.amount.with.capacity": "%s / %s мВ", "jei.tooltip.liquid.amount": "%s мВ", "jei.tooltip.transfer": "Переместить предметы", "jei.tooltip.recipe.tag": "Принимает любые: %s", "jei.tooltip.item.colors": "Цвета: %s", "jei.tooltip.shapeless.recipe": "Бесформенный рецепт", "jei.tooltip.cheat.mode.button.enabled": "Доступный режим жульничества", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Нажми \"%s\", чтобы вкл/выкл.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s сюда чтобы вкл/выкл.", "jei.tooltip.recipe.by": "Рецепты из: %s", "jei.tooltip.recipe.id": "ID рецепта: %s", "jei.tooltip.not.enough.space": "Здесь недостаточно места, чтобы отобразить список ингредиентов JEI.", "jei.tooltip.ingredient.list.disabled": "Наложения JEI отключены.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Нажми \"%s\", чтобы их включить.", "jei.tooltip.bookmarks": "Закладки JEI", "jei.tooltip.bookmarks.usage.nokey": "Добавь привязку клавиши для Закладок JEI в настройках управления.", "jei.tooltip.bookmarks.usage.key": "Наведи курсором на ингредиент и нажми \"%s\", чтобы добавить его в закладки.", "jei.tooltip.bookmarks.not.enough.space": "Здесь недостаточно места, чтобы отобразить список закладок JEI.", "jei.tooltip.bookmarks.recipe": "%s рецепта в закладке", "jei.tooltip.bookmarks.recipe.add": "Добавить рецепт в закладки.", "jei.tooltip.bookmarks.recipe.remove": "Убрать закладку этого рецепта.", "jei.tooltip.bookmarks.tooltips.usage": "[%s для деталей]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[%s чтобы создать один]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[%s чтобы создать много]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "Показывать сначала рецепты в закладках (включено)", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "Показывать сначала рецепты в закладках (выключено)", "jei.tooltip.recipe.sort.craftable.first.enabled": "Показывать сначала создаваемые рецепты (включено)", "jei.tooltip.recipe.sort.craftable.first.disabled": "Показывать сначала создаваемые рецепты (выключено)", "jei.tooltip.error.recipe.transfer.missing": "Отсутствуют предметы.", "jei.tooltip.error.recipe.transfer.inventory.full": "Инвентарь слишком полон.", "jei.tooltip.error.recipe.transfer.no.server": "Сервер должен иметь установленный JEI.", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Рецепт слишком объёмистый для создания в сетке создания игрока 2х2.", "jei.tooltip.error.crash": "Ошибка подсказки игредиента, см. файлы отчёта.", "jei.tooltip.error.render.crash": "Ошибка рендера ингредиента, см. файлы отчёта.", "jei.chat.error.no.cheat.permission.1": "Ты не имеешь разрешения на использование Режима жульничества в JEI.", "jei.chat.error.no.cheat.permission.disabled": "На этом сервере, режим жульничества в JEI выключен для всех игроков.", "jei.chat.error.no.cheat.permission.enabled": "На этом сервере могут использовать режим жульничества в JEI следующие профили игроков:", "jei.chat.error.no.cheat.permission.creative": "игроки, кто находится в Творческом режиме.", "jei.chat.error.no.cheat.permission.op": "игроки, кто имеет статус оператора (/op)", "jei.chat.error.no.cheat.permission.give": "игроки, кто может использовать /give", "jei.key.category.overlays": "JEI (наложения)", "key.jei.toggleOverlay": "Отобразить/скрыть наложения JEI", "key.jei.focusSearch": "Выбрать поле поиска", "key.jei.previousPage": "Предыдущая страница", "key.jei.nextPage": "Следующая страница", "key.jei.toggleBookmarkOverlay": "Отобразить/скрыть ингредиенты отмеченные закладкой", "jei.key.category.recipe.gui": "JEI (рецепты)", "key.jei.recipeBack": "Предыдущий рецепт", "key.jei.previousCategory": "Предыдущая категория рецепта", "key.jei.nextCategory": "Следующая категория рецепта", "key.jei.previousRecipePage": "Предыдущая страница рецепта", "key.jei.nextRecipePage": "Следующая страница рецепта", "key.jei.closeRecipeGui": "Закрыть интерфейс рецепта", "jei.key.category.cheat.mode": "JEI (режим жульничества)", "key.jei.toggleCheatMode": "Вкл/выкл режим жульничества", "key.jei.cheatOneItem": "Сжульничать 1 предмет", "key.jei.cheatOneItem2": "Сжульничать 1 предмет", "key.jei.cheatItemStack": "Сжульничать 1 стэк", "key.jei.cheatItemStack2": "Сжульничать 1 стэк", "jei.key.category.hover.config.button": "JEI (Наведение мышью на кнопку конфигурации)", "key.jei.toggleCheatModeConfigButton": "Вкл/выкл режим жульничества", "jei.key.category.edit.mode": "JEI (режим правки)", "key.jei.toggleEditMode": "Вкл/выкл режим скрытия ингредиента", "key.jei.toggleHideIngredient": "Скрыть ингредиент", "key.jei.toggleWildcardHideIngredient": "Скрыть ингредиент (специальный символ)", "jei.key.category.mouse.hover": "JEI (Наведение мышью)", "key.jei.bookmark": "Добавить/убрать закладку", "key.jei.showRecipe": "Отобразить рецепт", "key.jei.showRecipe2": "Отобразить рецепт", "key.jei.showUses": "Отобразить применения", "key.jei.showUses2": "Отобразить применения", "key.jei.transferRecipeBookmark": "Создать рецепт в закладке (один раз)", "key.jei.maxTransferRecipeBookmark": "Создать рецепт в закладке (макс. кол-во)", "jei.key.category.search": "JEI (фильтр поиска)", "key.jei.clearSearchBar": "Очистить фильтр поиска", "key.jei.previousSearch": "Предыдущий поиск", "key.jei.nextSearch": "Следующий поиск", "jei.key.category.dev.tools": "JEI (инструменты разработки)", "key.jei.copy.recipe.id": "Скопировать ID рецепта в буфер обмена", "jei.config": "Конфигурация JEI", "jei.config.default": "По умолчанию", "jei.config.valid": "Доступны", "jei.config.title": "Конфигурация %MODNAME", "jei.config.mode": "Режим", "jei.config.mode.description": "Измени режим, который работает в JEI.", "jei.config.mode.cheatItemsEnabled": "Режим жульничества", "jei.config.mode.cheatItemsEnabled.description": "Даёт предметы вместо отображения рецепта.", "jei.config.mode.editEnabled": "Режим скрытия ингредиентов", "jei.config.mode.editEnabled.description": "Скрывает и раскрывает ингредиенты путём нажатием по нему в списке ингредиентов.", "jei.config.interface": "Интерфейс", "jei.config.interface.description": "Параметры, связанные с интерфейсом пользователя.", "jei.config.interface.overlayEnabled": "Отобразить список ингредиентов", "jei.config.interface.overlayEnabled.description": "Отображает список ингредиентов рядом с открытыми интерфейсами.", "jei.config.interface.bookmarkOverlayEnabled": "Отобразить список закладок", "jei.config.interface.bookmarkOverlayEnabled.description": "Отображает список закладок рядом с открытыми интерфейсами.", "jei.config.client.search": "Параметры поиска", "jei.config.client.search.description": "Параметры, связанные с полем поиска.", "jei.config.client.search.modNameSearchMode": "@Название мода", "jei.config.client.search.modNameSearchMode.description": "Режим поиска по названиям мода (префикс: @).", "jei.config.client.search.tooltipSearchMode": "$Подсказка", "jei.config.client.search.tooltipSearchMode.description": "Режим поиска по подсказкам (префикс: $).", "jei.config.client.search.tagSearchMode": "#Тег", "jei.config.client.search.tagSearchMode.description": "Режим поиска по тегам (префикс: #).", "jei.config.client.search.creativeTabSearchMode": "%%Творческая вкладка", "jei.config.client.search.creativeTabSearchMode.description": "Режим поиска по названиям творческих вкладок (префикс: %).", "jei.config.client.search.colorSearchMode": "^Цвет", "jei.config.client.search.colorSearchMode.description": "Режим поиска по цветам (префикс: ^).", "jei.config.client.search.resourceLocationSearchMode": "&Идентификатор ресурса", "jei.config.client.search.resourceLocationSearchMode.description": "Режим поиска по идентификаторам ресурсов (префикс: &).", "jei.config.client.search.searchAdvancedTooltips": "Подсказки для расширенного поиска", "jei.config.client.advanced": "Расширенные", "jei.config.client.advanced.description": "Параметры расширенной конфигурации для способа изменения функциональности JEI.", "jei.config.client.advanced.itemBlacklist": "Чёрный список ингредиентов", "jei.config.client.advanced.itemBlacklist.description": "Список ингредиентов, который не должен отображаться в списке ингредиентов. Формат: идентификатор мода[:название[:мета-данная]]. Режим скрытия ингредиентов будет автоматически добавлять или убирать записи.", "jei.config.debug.debug.debugMode": "Режим отладки", "jei.config.debug.debug.debugMode.description": "Полезен только для разработчиков JEI, добавляет тестовые ингредиенты и несколько рецептов для отладки.", "jei.config.client.appearance.centerSearch": "Поле поиска по центру", "jei.config.client.appearance.centerSearch.description": "Перемещает поле поиска JEI в центр нижней части экрана.", "jei.config.modIdFormat.modName.modNameFormat": "Формат названия мода", "jei.config.modIdFormat.modName.modNameFormat.description": "Какое название мода должно быть размечено в подсказке для интерфейсов JEI. Оставь пустым, чтобы отключить.", "jei.config.client.advanced.maxColumns": "Максимальная ширина наложения", "jei.config.client.advanced.maxColumns.description": "Максимальная ширина списка ингредиента и закладки.", "jei.config.client.appearance.recipeGuiHeight": "Максимальная высота интерфейса рецепта", "jei.config.client.appearance.recipeGuiHeight.description": "Максимальная высота интерфейса рецепта.", "jei.config.client.cheating.giveMode": "Режим выдачи", "jei.config.client.cheating.giveMode.description": "Выбери, должен ли JEI давать ингредиенты прямо в инвентарь либо собирать их мышкой.", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "Добавлять новые закладки в начало", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "Когда включено, добавляет новые закладки в начало списка закладок. Когда выключено, добавляет новые закладки в конец списка закладок", "gui.jei.editMode.description": "Режим скрытия ингредиентов JEI:", "gui.jei.editMode.description.hide": "%s, чтобы скрыть.", "gui.jei.editMode.description.hide.wild": "%s, чтобы скрыть символом.", "gui.jei.category.craftingTable": "Создание", "gui.jei.category.stoneCutter": "Резка камня", "gui.jei.category.smelting": "Плавка", "gui.jei.category.smoking": "Копчение", "gui.jei.category.blasting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.campfire": "Приготовление на костре", "gui.jei.category.smelting.experience": "Опыт: %s", "gui.jei.category.smelting.time.seconds": "%s секунд", "gui.jei.category.fuel": "Топливо", "gui.jei.category.fuel.smeltCount.single": "Плавит 1 предмет", "gui.jei.category.fuel.smeltCount": "Плавит %s предметов", "gui.jei.category.brewing": "Варка", "gui.jei.category.brewing.steps": "Шаги: %s", "gui.jei.category.compostable": "Биоразлагаемость", "gui.jei.category.compostable.chance": "Вероятность: %s%%", "gui.jei.category.itemInformation": "Сведения", "jei.message.configured": "Установи мод \"Configured\" для доступа к внутриигровой конфигурации.", "jei.message.config.folder": "Нажмите здесь, чтобы открыть папку конфигурации JEI", "jei.message.copy.recipe.id.success": "Идентификатор рецепта скопирован в буфер обмена: %s", "jei.message.copy.recipe.id.failure": "Не удалось скопировать идентификатор рецепта в буфер обмена, идентификатор рецепта неизвестен.", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "jei.key.shift": "SHIFT", "jei.key.mouse.left": "ЛКМ", "jei.key.mouse.right": "ПКМ", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}