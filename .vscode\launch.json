{"version": "0.2.0", "configurations": [{"type": "java", "request": "launch", "name": "NeoForge - Client", "presentation": {"group": "Mod Development - NeoForge", "order": 0}, "projectName": "NeoForge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\build\\moddev\\clientRunProgramArgs.txt"], "vmArgs": ["@B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\build\\moddev\\clientRunVmArgs.txt", "-Dfml.modFolders=jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Core\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Common\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\CommonApi\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Library\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Gui\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForgeApi\\bin\\main"], "cwd": "${workspaceFolder}\\NeoForge\\run\\client\\Dev", "env": {}, "console": "internalConsole", "shortenCommandLine": "none"}, {"type": "java", "request": "launch", "name": "NeoForge - Client_01", "presentation": {"group": "Mod Development - NeoForge", "order": 1}, "projectName": "NeoForge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\build\\moddev\\client_01RunProgramArgs.txt"], "vmArgs": ["@B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\build\\moddev\\client_01RunVmArgs.txt", "-Dfml.modFolders=jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Core\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Common\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\CommonApi\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Library\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Gui\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForgeApi\\bin\\main"], "cwd": "${workspaceFolder}\\NeoForge\\run\\client\\Player01", "env": {}, "console": "internalConsole", "shortenCommandLine": "none"}, {"type": "java", "request": "launch", "name": "NeoForge - Server", "presentation": {"group": "Mod Development - NeoForge", "order": 2}, "projectName": "NeoForge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\build\\moddev\\serverRunProgramArgs.txt"], "vmArgs": ["@B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\build\\moddev\\serverRunVmArgs.txt", "-Dfml.modFolders=jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForge\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Core\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Common\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\CommonApi\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Library\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\Gui\\bin\\main;jei%%B:\\Google Chrome\\JustEnoughItems-1.21.7\\JustEnoughItems-1.21.7\\NeoForgeApi\\bin\\main"], "cwd": "${workspaceFolder}\\NeoForge\\run\\server", "env": {}, "console": "internalConsole", "shortenCommandLine": "none"}]}