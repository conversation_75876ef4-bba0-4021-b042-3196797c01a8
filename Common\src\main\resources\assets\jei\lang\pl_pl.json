{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Konfiguracja JEI", "jei.tooltip.show.recipes": "Pokaż receptury", "jei.tooltip.show.all.recipes.hotkey": "Naciś<PERSON>j „%s”, aby pokazać wszystkie receptury.", "jei.tooltip.delete.item": "<PERSON><PERSON><PERSON><PERSON>, aby <PERSON>.", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.liquid.flowing": "%s (w postaci płynącej)", "jei.tooltip.transfer": "Przenieś przedmioty", "jei.tooltip.recipe.tag": "Przyjmuje tag: %s", "jei.tooltip.item.colors": "Kolory: %s", "jei.tooltip.item.search.aliases": "Aliasy wyszukiwania:", "jei.tooltip.shapeless.recipe": "Bezkształtna receptura", "jei.tooltip.cheat.mode.button.enabled": "Tryb cheatów włączony.", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Naciśnij „%s”, aby go przełączyć.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "Naciśnij tutaj „%s”, aby go przełączyć.", "jei.tooltip.recipe.by": "Receptura z: %s", "jei.tooltip.recipe.id": "ID receptury: %s", "jei.tooltip.not.enough.space": "O<PERSON>zar po prawej stronie tego ekranu jest zbyt mały, żeby wyświetlić nakładkę z listą składników JEI.", "jei.tooltip.ingredient.list.disabled": "Nakładki JEI są ukryte.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Naciś<PERSON>j „%s”, aby z powrotem je pokazać.", "jei.tooltip.bookmarks": "Zakładki JEI", "jei.tooltip.bookmarks.usage.nokey": "Przypisz klawisz dla zakładek JEI w swoich ustawieniach sterowania.", "jei.tooltip.bookmarks.usage.key": "Najedź kursorem na składnik i naciśnij „%s”, aby dodać go do zakładek.", "jei.tooltip.bookmarks.not.enough.space": "<PERSON><PERSON><PERSON> po lewej stronie tego ekranu jest zbyt mały, żeby wyświetlić nakładkę z listą zakładek JEI.", "jei.tooltip.bookmarks.recipe": "%s - zakładka receptury", "jei.tooltip.bookmarks.recipe.add": "Dodaj recepturę do zakładek.", "jei.tooltip.bookmarks.recipe.remove": "Usuń recepturę z zakładek.", "jei.tooltip.bookmarks.tooltips.usage": "[<PERSON><PERSON><PERSON><PERSON><PERSON> „%s”, by w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> szczegół<PERSON>]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[<PERSON><PERSON><PERSON><PERSON><PERSON> „%s”, by s<PERSON><PERSON><PERSON><PERSON><PERSON> jed<PERSON> sztuk<PERSON>]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[<PERSON><PERSON><PERSON><PERSON><PERSON> „%s”, by s<PERSON><PERSON><PERSON><PERSON><PERSON> wiele sztuk]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "Pokazywanie w pierwszej kolejności receptur dodanych do zakładek (włączone).", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "Pokazywanie w pierwszej kolejności receptur dodanych do zakładek (wyłączone).", "jei.tooltip.recipe.sort.craftable.first.enabled": "Pokazywanie w pierwszej kolejności receptur możliwych do wytworzenia (włączone).", "jei.tooltip.recipe.sort.craftable.first.disabled": "Pokazywanie w pierwszej kolejności receptur możliwych do wytworzenia (wyłączone).", "jei.tooltip.error.recipe.transfer.missing": "Brakuje przedmiotów", "jei.tooltip.error.recipe.transfer.inventory.full": "Ekwipunek jest przepełniony.", "jei.tooltip.error.recipe.transfer.no.server": "Serwer musi mieć zainstalowane JEI.", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Receptura jest za duża, by m<PERSON><PERSON><PERSON> by<PERSON><PERSON> to wytworzyć w siatce wytwarzania 2x2 gracza.", "jei.tooltip.error.crash": "Ten składnik uległ awarii podczas odczytywania jego tooltipa. Sprawdź logi klienta, aby dowiedzieć się więcej.", "jei.tooltip.error.render.crash": "Ten składnik uległ awarii podczas jego renderowania. Sprawdź logi klienta, aby dowiedzieć się więcej.", "jei.chat.error.no.cheat.permission.1": "Nie masz uprawnień do korzystania z trybu cheatów JEI.", "jei.chat.error.no.cheat.permission.disabled": "Na tym serwerze jest on wyłączony dla wszystkich graczy.", "jei.chat.error.no.cheat.permission.enabled": "Na tym serwerze wyłącznie następujące typy graczy mogą z niego korzystać:", "jei.chat.error.no.cheat.permission.creative": "gracze, którzy są w trybie kreatywnym", "jei.chat.error.no.cheat.permission.op": "gracze, którzy mają status operatora (/op)", "jei.chat.error.no.cheat.permission.give": "gracze, którzy mogą używać polecenia „/give”", "jei.key.category.overlays": "JEI (nakładki)", "key.jei.toggleOverlay": "Pokaż/ukryj nakładki JEI", "key.jei.focusSearch": "Zaznacz pasek wyszukiwania", "key.jei.previousPage": "Poprzednia strona", "key.jei.nextPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "key.jei.toggleBookmarkOverlay": "Pokaż/ukryj składniki z zakładek", "jei.key.category.recipe.gui": "JEI (receptury)", "key.jei.recipeBack": "Poprzednia receptura", "key.jei.previousCategory": "Poprzednia kategoria receptur", "key.jei.nextCategory": "Nast<PERSON><PERSON><PERSON> kategoria receptur", "key.jei.previousRecipePage": "Poprzednia strona z recepturami", "key.jei.nextRecipePage": "Następna strona z recepturami", "key.jei.closeRecipeGui": "Zamknij interfejs z recepturami", "jei.key.category.cheat.mode": "JEI (tryb cheatów)", "key.jei.toggleCheatMode": "Przełącz tryb cheatów", "key.jei.cheatOneItem": "Zcheatuj 1 przedmiot", "key.jei.cheatOneItem2": "Zcheatuj 1 przedmiot", "key.jei.cheatItemStack": "Zcheatuj 1 stack", "key.jei.cheatItemStack2": "Zcheatuj 1 stack", "jei.key.category.hover.config.button": "JEI (najechanie kursorem na przycisk konfiguracji)", "key.jei.toggleCheatModeConfigButton": "Przełącz tryb cheatów", "jei.key.category.edit.mode": "JEI (tryb edycji)", "key.jei.toggleEditMode": "Przełącz tryb ukrywania składników", "key.jei.toggleHideIngredient": "Ukryj składnik", "key.jei.toggleWildcardHideIngredient": "Ukryj składnik (z wieloznacznikiem)", "jei.key.category.mouse.hover": "JEI (najechanie kursorem myszy)", "key.jei.bookmark": "Dodaj/usuń zakładkę", "key.jei.showRecipe": "Pokaż recepturę", "key.jei.showRecipe2": "Pokaż recepturę", "key.jei.showUses": "Pokaż zastosowania", "key.jei.showUses2": "Pokaż zastosowania", "key.jei.transferRecipeBookmark": "Stwórz recepturę z zakładek (jedną)", "key.jei.maxTransferRecipeBookmark": "Stwórz recepturę z zakładek (wiele)", "jei.key.category.search": "JEI (filtr wyszukiwania)", "key.jei.clearSearchBar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filtr wyszukiwania", "key.jei.previousSearch": "Poprzednie wyszukiwanie", "key.jei.nextSearch": "Następne wyszukiwanie", "jei.key.category.dev.tools": "JEI (narzędzia programistyczne)", "key.jei.copy.recipe.id": "Kopiuj ID receptury do schowka", "jei.config": "Konfiguracja JEI", "jei.config.default": "Domy<PERSON><PERSON><PERSON>", "jei.config.valid": "Poprawne", "jei.config.title": "Konfiguracja %MODNAME", "jei.config.mode": "<PERSON><PERSON>", "jei.config.mode.description": "<PERSON><PERSON><PERSON>, w jakim działa J<PERSON>.", "jei.config.mode.cheatItemsEnabled": "<PERSON><PERSON>", "jei.config.mode.cheatItemsEnabled.description": "Dawanie przedmiotów zamiast pokazywania ich receptur.", "jei.config.mode.editEnabled": "Tryb ukrywania składników", "jei.config.mode.editEnabled.description": "Ukrywanie lub odkrywanie składników po kliknięciu ich na nakładce z listą składników.", "jei.config.interface": "Interfejs", "jei.config.interface.description": "Opcje dotyczące interfejsu użytkownika.", "jei.config.interface.overlayEnabled": "Pokazywanie nakładki z listą składników", "jei.config.interface.overlayEnabled.description": "Pokazywanie nakładki z listą składników obok otwartych okienek interfejsu.", "jei.config.interface.bookmarkOverlayEnabled": "Pokazywanie nakładki z listą zakładek", "jei.config.interface.bookmarkOverlayEnabled.description": "Pokazywanie nakładki z listą zakładek obok otwartych okienek interfejsu.", "jei.config.client.search": "<PERSON><PERSON>je w<PERSON>zukiwan<PERSON>", "jei.config.client.search.description": "Opcje dotyczące paska wyszukiwania.", "jei.config.client.search.modNameSearchMode": "@NazwaModa", "jei.config.client.search.modNameSearchMode.description": "Tryb wyszukiwania po nazwach modów (prefiks: @).", "jei.config.client.search.tooltipSearchMode": "$Tooltip", "jei.config.client.search.tooltipSearchMode.description": "Tryb wyszukiwania po tooltipach (prefiks: $).", "jei.config.client.search.tagSearchMode": "#Tag", "jei.config.client.search.tagSearchMode.description": "<PERSON>b wyszukiwania po tagach (prefiks: #).", "jei.config.client.search.creativeTabSearchMode": "%%KartaTrybuKreatywnego", "jei.config.client.search.creativeTabSearchMode.description": "Tryb wyszukiwania po nazwach kart z trybu kreatywnego (prefiks: %).", "jei.config.client.search.colorSearchMode": "^<PERSON><PERSON>", "jei.config.client.search.colorSearchMode.description": "Tryb wyszukiwania po kolo<PERSON>h (prefiks: ^).", "jei.config.client.search.resourceLocationSearchMode": "&LokalizacjaZasobu", "jei.config.client.search.resourceLocationSearchMode.description": "Tryb wyszukiwania po lokalizacjach zasobów (prefiks: &).", "jei.config.client.search.searchAdvancedTooltips": "Wyszukiwanie w zaawansowanych tooltipach", "jei.config.client.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.client.advanced.description": "Zaawansowane opcje konfiguracyjne do zmiany sposobu, w jaki funkcjonuje JEI.", "jei.config.client.advanced.itemBlacklist": "Czarna lista składników", "jei.config.client.advanced.itemBlacklist.description": "Lista składników, które mają nie być wyświetlane na nakładce z listą składników. Format: idModa[:nazwa[:meta]]. Tryb ukrywania składników automatycznie doda tutaj wpisy lub je usunie.", "jei.config.debug.debug.debugMode": "Tryb debugowania", "jei.config.debug.debug.debugMode.description": "Przydatny jedynie dla programistów JEI, dodaje testowe składniki i kilka receptur pomocnych przy debugowaniu.", "jei.config.client.appearance.centerSearch": "Wyśrodkowany pasek wyszukiwania", "jei.config.client.appearance.centerSearch.description": "Przeniesienie paska wyszukiwania JEI na środek dolnej krawędzi ekranu.", "jei.config.modIdFormat.modName.modNameFormat": "Format nazw modów", "jei.config.modIdFormat.modName.modNameFormat.description": "Format nazw modów w tooltipach interfejsu JEI. Pozostaw puste, aby w<PERSON>.", "jei.config.client.advanced.maxColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.client.advanced.maxColumns.description": "Maksymalna szerokość nakładek z listą składników oraz z listą zakładek.", "jei.config.client.appearance.recipeGuiHeight": "Maksymalna wysokość interfejsu", "jei.config.client.appearance.recipeGuiHeight.description": "Maksymalna wysokość interfejsu z recepturami.", "jei.config.client.cheating.giveMode": "<PERSON><PERSON>", "jei.config.client.cheating.giveMode.description": "<PERSON><PERSON><PERSON><PERSON>, czy JEI ma dawać składniki bezpośrednio do ekwipunku, czy wolisz samemu zabierać i przenosić je za pomocą myszy.", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "Umieszczanie nowych zakładek na początku", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "Dodawanie nowych zakładek na początku listy zakładek zamiast na jej końcu.", "gui.jei.editMode.description": "Tryb ukrywania składników JEI:", "gui.jei.editMode.description.hide": "<PERSON><PERSON><PERSON><PERSON><PERSON> „%s”, aby <PERSON>.", "gui.jei.editMode.description.hide.wild": "Naciśnij „%s”, aby ukryć za pomocą wieloznacznika.", "gui.jei.category.craftingTable": "Wytwarzanie", "gui.jei.category.stoneCutter": "Obróbka kamienia", "gui.jei.category.smelting": "Przetapianie", "gui.jei.category.smoking": "Wę<PERSON><PERSON><PERSON>", "gui.jei.category.blasting": "Wytapianie", "gui.jei.category.campfire": "Pieczenie przy ognisku", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%s s", "gui.jei.category.fuel": "Paliwo", "gui.jei.category.fuel.smeltCount.single": "Przetapia: 1", "gui.jei.category.fuel.smeltCount": "Przetapia: %s", "gui.jei.category.brewing": "<PERSON><PERSON><PERSON>", "gui.jei.category.brewing.steps": "Kroki: %s", "gui.jei.category.compostable": "Kompostowanie", "gui.jei.category.compostable.chance": "Szansa: %s%%", "gui.jei.category.itemInformation": "Informacje", "gui.jei.category.tagInformation": "Tagi typu „%s”", "gui.jei.category.tagInformation.block": "Tagi bloków", "gui.jei.category.tagInformation.fluid": "Tagi płynów", "gui.jei.category.tagInformation.item": "Tagi przedmiotów", "gui.jei.category.recipe.crashed": "Ta receptura uległa awarii. Sprawdź logi klienta, aby dowiedzie<PERSON> się więcej.", "jei.message.configured": "Zainstaluj moda Configured, aby uzyskać dostęp do konfiguracji w grze", "jei.message.config.folder": "<PERSON><PERSON> k<PERSON> t<PERSON>, żeby zamiast tego otworzyć folder konfiguracyjny JEI", "jei.message.copy.recipe.id.success": "Skopiowano do schowka następujące ID receptury: %s", "jei.message.copy.recipe.id.failure": "<PERSON><PERSON> udało się skopiować do schowka ID receptury, g<PERSON><PERSON> jest ono nieznane", "jei.key.combo.shift": "Shift + %s", "jei.key.combo.control": "Ctrl + %s", "jei.key.combo.command": "Cmd + %s", "jei.key.combo.alt": "Alt + %s", "jei.key.shift": "Shift", "jei.key.mouse.left": "Lewy przyc.", "jei.key.mouse.right": "Prawy przyc.", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}