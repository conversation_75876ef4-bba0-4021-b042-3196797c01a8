{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "JEI Config", "jei.tooltip.show.recipes": "Show Recipes", "jei.tooltip.show.all.recipes.hotkey": "Press \"%s\" to show all recipes.", "jei.tooltip.delete.item": "Click to delete.", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.liquid.flowing": "%s (Flowing)", "jei.tooltip.transfer": "Move Items", "jei.tooltip.recipe.tag": "Accepts Tag: %s", "jei.tooltip.item.colors": "Colors: %s", "jei.tooltip.item.search.aliases": "Search Aliases:", "jei.tooltip.shapeless.recipe": "<PERSON><PERSON><PERSON><PERSON>", "jei.tooltip.cheat.mode.button.enabled": "Cheat mode enabled.", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Press \"%s\" to toggle it.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "Press \"%s\" here to toggle it.", "jei.tooltip.recipe.by": "Recipe By: %s", "jei.tooltip.recipe.id": "Recipe ID: %s", "jei.tooltip.not.enough.space": "The area on the right-hand side of this screen is too small for the JEI ingredient list overlay to display.", "jei.tooltip.ingredient.list.disabled": "JEI overlays are hidden.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Press \"%s\" to show them again.", "jei.tooltip.bookmarks": "JEI Bookmarks", "jei.tooltip.bookmarks.usage.nokey": "Add a key binding for JEI bookmarks in your Controls settings.", "jei.tooltip.bookmarks.usage.key": "Hover over an ingredient and press \"%s\" to bookmark it.", "jei.tooltip.bookmarks.not.enough.space": "The area on the left-hand side of this screen is too small for the JEI bookmark list overlay to display.", "jei.tooltip.bookmarks.recipe": "%s Recipe Bookmark", "jei.tooltip.bookmarks.recipe.add": "Bookmark this recipe.", "jei.tooltip.bookmarks.recipe.remove": "Remove the bookmark for this recipe.", "jei.tooltip.bookmarks.tooltips.usage": "[Press \"%s\" to show details]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[Press \"%s\" to craft one]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[Press \"%s\" to craft many]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "Show bookmarked recipes first (enabled).", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "Show bookmarked recipes first (disabled).", "jei.tooltip.recipe.sort.craftable.first.enabled": "Show craftable recipes first (enabled).", "jei.tooltip.recipe.sort.craftable.first.disabled": "Show craftable recipes first (disabled).", "jei.tooltip.error.recipe.transfer.missing": "Missing Items", "jei.tooltip.error.recipe.transfer.inventory.full": "Inventory is too full.", "jei.tooltip.error.recipe.transfer.no.server": "The server must have JEI installed.", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "Recipe is too large to craft in the 2x2 player crafting grid.", "jei.tooltip.error.crash": "This ingredient crashed when getting its tooltip. Please see the client logs for details.", "jei.tooltip.error.render.crash": "This ingredient crashed when being rendered. Please see the client logs for details.", "jei.chat.error.no.cheat.permission.1": "You do not have permission to use JEI's cheat mode.", "jei.chat.error.no.cheat.permission.disabled": "On this server, it is disabled for all players.", "jei.chat.error.no.cheat.permission.enabled": "On this server, only the following types of players can use it:", "jei.chat.error.no.cheat.permission.creative": "players who are in the creative mode", "jei.chat.error.no.cheat.permission.op": "players who have an operator status (/op)", "jei.chat.error.no.cheat.permission.give": "players who can use the \"/give\" command", "jei.key.category.overlays": "JEI (Overlays)", "key.jei.toggleOverlay": "Show/Hide JEI Overlays", "key.jei.focusSearch": "Select Search Bar", "key.jei.previousPage": "Previous Page", "key.jei.nextPage": "Next Page", "key.jei.toggleBookmarkOverlay": "Show/Hide Bookmarked Ingredients", "jei.key.category.recipe.gui": "JEI (Recipes)", "key.jei.recipeBack": "Previous Recipe", "key.jei.previousCategory": "Previous Recipe Category", "key.jei.nextCategory": "Next Recipe Category", "key.jei.previousRecipePage": "Previous Recipe Page", "key.jei.nextRecipePage": "Next Recipe Page", "key.jei.closeRecipeGui": "Close Recipes GUI", "jei.key.category.cheat.mode": "JEI (Cheat Mode)", "key.jei.toggleCheatMode": "Toggle Cheat Mode", "key.jei.cheatOneItem": "Cheat 1 Item", "key.jei.cheatOneItem2": "Cheat 1 Item", "key.jei.cheatItemStack": "Cheat 1 Stack", "key.jei.cheatItemStack2": "Cheat 1 Stack", "jei.key.category.hover.config.button": "JEI (Hovering With Mouse Over Config <PERSON>)", "key.jei.toggleCheatModeConfigButton": "Toggle Cheat Mode", "jei.key.category.edit.mode": "JEI (Edit Mode)", "key.jei.toggleEditMode": "Toggle Hide Ingredients Mode", "key.jei.toggleHideIngredient": "Hide Ingredient", "key.jei.toggleWildcardHideIngredient": "<PERSON><PERSON> (With Wildcard)", "jei.key.category.mouse.hover": "JEI (Hovering With Mouse)", "key.jei.bookmark": "Add/Remove Bookmark", "key.jei.showRecipe": "Show Recipe", "key.jei.showRecipe2": "Show Recipe", "key.jei.showUses": "Show Uses", "key.jei.showUses2": "Show Uses", "key.jei.transferRecipeBookmark": "Craft Bookmarked Recipe (One)", "key.jei.maxTransferRecipeBookmark": "Craft Bookmarked Recipe (Many)", "jei.key.category.search": "JEI (Search Filter)", "key.jei.clearSearchBar": "Clear Search Filter", "key.jei.previousSearch": "Previous Search", "key.jei.nextSearch": "Next Search", "jei.key.category.dev.tools": "JEI (Dev Tools)", "key.jei.copy.recipe.id": "Copy Recipe ID to Clipboard", "jei.config": "JEI Config", "jei.config.name": "Name: %s", "jei.config.description": "Description: %s", "jei.config.valueValues": "Valid Values: %s", "jei.config.defaultValue": "Default Value: %s", "jei.config.title": "%MODNAME Config", "jei.config.default": "<PERSON><PERSON><PERSON>", "jei.config.valid": "<PERSON><PERSON>", "jei.config.mode": "Mode", "jei.config.mode.description": "Change the mode that JEI is operating in.", "jei.config.mode.cheatItemsEnabled": "Cheat Mode", "jei.config.mode.cheatItemsEnabled.description": "Give items instead of showing the recipe.", "jei.config.mode.editEnabled": "Hide Ingredients Mode", "jei.config.mode.editEnabled.description": "Hide or unhide ingredients by clicking them in the ingredient list overlay.", "jei.config.interface": "Interface", "jei.config.interface.description": "Options related to the user interface.", "jei.config.interface.overlayEnabled": "Show Ingredient List Overlay", "jei.config.interface.overlayEnabled.description": "Show the ingredient list overlay next to open GUIs.", "jei.config.interface.bookmarkOverlayEnabled": "Show Bookmark List Overlay", "jei.config.interface.bookmarkOverlayEnabled.description": "Show the bookmark list overlay next to open GUIs.", "jei.config.client.appearance": "Appearance", "jei.config.client.appearance.description": "Config options to change the appearance of JEI.", "jei.config.client.appearance.centerSearch": "Center Search Bar", "jei.config.client.appearance.centerSearch.description": "Move the JEI search bar to the bottom center of the screen.", "jei.config.client.appearance.recipeGuiHeight": "Recipe GUI Height", "jei.config.client.appearance.recipeGuiHeight.description": "The maximum height for the Recipes Gui (in pixels).", "jei.config.client.cheating": "Cheating", "jei.config.client.cheating.description": "Config options related to Cheating.", "jei.config.client.cheating.giveMode": "Give Mode", "jei.config.client.cheating.giveMode.description": "Choose if JEI should give ingredients directly to the inventory or pick them up with the mouse.", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled": "Cheat Items to Hotbar Using Hotkeys", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled.description": "Enable cheating items into the hotbar by using Shift + numeric keys.", "jei.config.client.cheating.showHiddenIngredients": "Show Hidden Ingredients", "jei.config.client.cheating.showHiddenIngredients.description": "Enable showing ingredients that are not in the creative menu.", "jei.config.client.cheating.showTagRecipesEnabled": "Show Tag Recipes", "jei.config.client.cheating.showTagRecipesEnabled.description": "Show recipes for ingredient tags like item tags and block tags.", "jei.config.client.bookmarks": "Bookmarks", "jei.config.client.bookmarks.description": "Config options related to Bookmarking ingredients and recipes.", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "Add Bookmarks to Front", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "When true, add new bookmarks to the front of the list. When false, add them to the end.", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled": "Drag To Rearrange Bookmarks", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled.description": "Enable dragging bookmarks to rearrange them in the list.", "jei.config.client.tooltips": "Tooltips", "jei.config.client.tooltips.description": "Config options related to Tooltips in JEI.", "jei.config.client.tooltips.bookmarkTooltipFeatures": "Bookmarks Tooltips Features", "jei.config.client.tooltips.bookmarkTooltipFeatures.description": "Extra features for bookmark tooltips.", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures": "Shift for Bookmarks Tooltips", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures.description": "Hold Shift to show bookmark tooltip features.", "jei.config.client.tooltips.showCreativeTabNamesEnabled": "Show Creative Tab Names", "jei.config.client.tooltips.showCreativeTabNamesEnabled.description": "Show creative tab names in ingredient tooltips.", "jei.config.client.tooltips.tagContentTooltipEnabled": "Show Tag Contents", "jei.config.client.tooltips.tagContentTooltipEnabled.description": "Show tag content in tooltips when browsing recipe ingredients.", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled": "Hide Single-Ingredient Tag Contents", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled.description": "Hide tag content in tooltips when there is only one ingredient in the tag.", "jei.config.client.lookups": "Lookups", "jei.config.client.lookups.description": "Config options related to looking up uses and recipes for ingredients in JEI.", "jei.config.client.lookups.lookupFluidContentsEnabled": "Lookup Fluid Contents", "jei.config.client.lookups.lookupFluidContentsEnabled.description": "When looking up recipes with items that contain fluids, also look up recipes for the fluids.", "jei.config.client.lookups.lookupBlockTagsEnabled": "Lookup ItemBlock Tags", "jei.config.client.lookups.lookupBlockTagsEnabled.description": "When searching for item tags, also include tags for the default blocks contained in the items.", "jei.config.client.input": "Input", "jei.config.client.input.description": "Config options related to Inputs in JEI.", "jei.config.client.input.dragDelayInMilliseconds": "Drag Delay", "jei.config.client.input.dragDelayInMilliseconds.description": "Number of milliseconds before a long mouse click is considered dragging the mouse.", "jei.config.client.input.smoothScrollRate": "Smooth Scroll Rate", "jei.config.client.input.smoothScrollRate.description": "Scroll rate for scrolling the mouse wheel in smooth-scrolling scroll boxes. Measured in pixels.", "jei.config.client.performance": "Performance", "jei.config.client.performance.description": "Config options related to performance optimizations in JEI.", "jei.config.client.performance.lowMemorySlowSearchEnabled": "Low Memory Search", "jei.config.client.performance.lowMemorySlowSearchEnabled.description": "Set search to low-memory mode (makes search slow but uses less RAM).", "jei.config.client.advanced": "Advanced", "jei.config.client.advanced.description": "Advanced config options to change the way JEI functions.", "jei.config.client.advanced.catchRenderErrorsEnabled": "Catch Render Errors", "jei.config.client.advanced.catchRenderErrorsEnabled.description": "Catch render errors from modded ingredients and attempt to recover from them instead of crashing.", "jei.config.client.sorting": "Sorting", "jei.config.client.sorting.description": "Config options related to how JEI sorts recipes and ingredients.", "jei.config.client.sorting.ingredientSortStages": "Ingredient Sorting Stages", "jei.config.client.sorting.ingredientSortStages.description": "Sorting order for the ingredient list.", "jei.config.client.sorting.recipeSorterStages": "Recipe Sorting Stages", "jei.config.client.sorting.recipeSorterStages.description": "Sorting order for displayed recipes.", "jei.config.client.search": "Search", "jei.config.client.search.description": "Config options related to how JEI searches recipes.", "jei.config.client.search.modNameSearchMode": "@Mod Name Search Mode", "jei.config.client.search.modNameSearchMode.description": "Search mode for mod names (prefix: @).", "jei.config.client.search.tagSearchMode": "#Tag Search Mode", "jei.config.client.search.tagSearchMode.description": "Search mode for tags (prefix: #).", "jei.config.client.search.tooltipSearchMode": "$Tooltip Search Mode", "jei.config.client.search.tooltipSearchMode.description": "Search mode for tooltips (prefix: $).", "jei.config.client.search.colorSearchMode": "^Color Search Mode", "jei.config.client.search.colorSearchMode.description": "Search mode for colors (prefix: ^).", "jei.config.client.search.resourceLocationSearchMode": "&Resource Location Search Mode", "jei.config.client.search.resourceLocationSearchMode.description": "Search mode for resource locations (prefix: &).", "jei.config.client.search.creativeTabSearchMode": "%Creative Tab Search Mode", "jei.config.client.search.creativeTabSearchMode.description": "Search mode for creative mode tab names (prefix: %).", "jei.config.client.search.searchAdvancedTooltips": "Search Advanced Tooltips", "jei.config.client.search.searchAdvancedTooltips.description": "Search in advanced tooltips (visible with F3 + H).", "jei.config.client.search.searchModIds": "Search Mod Ids", "jei.config.client.search.searchModIds.description": "Search mod IDs in addition to mod names.", "jei.config.client.search.searchModAliases": "Search Mod Aliases", "jei.config.client.search.searchModAliases.description": "Search mod aliases (alternative names) that are added by plugins, in addition to mod names.", "jei.config.client.search.searchShortModNames": "Search Short Mod Names", "jei.config.client.search.searchShortModNames.description": "Search by the shorthand first letters of a mod's name.", "jei.config.client.search.searchIngredientAliases": "Search Ingredient Aliases", "jei.config.client.search.searchIngredientAliases.description": "Search ingredient aliases (alternative names) that are added by plugins, in addition to ingredient names.", "jei.config.client.ingredientList": "Ingredient List", "jei.config.client.ingredientList.description": "Config options related to the Ingredient List (the list of ingredients on the right side of the screen)", "jei.config.client.ingredientList.maxRows": "<PERSON>s", "jei.config.client.ingredientList.maxRows.description": "Max number of rows shown.", "jei.config.client.ingredientList.maxColumns": "<PERSON>", "jei.config.client.ingredientList.maxColumns.description": "Max number of columns shown.", "jei.config.client.ingredientList.horizontalAlignment": "Horizontal Alignment", "jei.config.client.ingredientList.horizontalAlignment.description": "Horizontal alignment of the ingredient list inside the available area.", "jei.config.client.ingredientList.verticalAlignment": "Vertical Alignment", "jei.config.client.ingredientList.verticalAlignment.description": "Vertical alignment of the ingredient list inside the available area.", "jei.config.client.ingredientList.buttonNavigationVisibility": "Navigation Visibility", "jei.config.client.ingredientList.buttonNavigationVisibility.description": "Visibility of the top page buttons. Use AUTO_HIDE to only show it when there are multiple pages.", "jei.config.client.ingredientList.drawBackground": "Draw GUI Background", "jei.config.client.ingredientList.drawBackground.description": "Enable this to draw a background texture behind the ingredient list.", "jei.config.client.bookmarkList": "Bookmark List", "jei.config.client.bookmarkList.description": "Config options related to the Bookmark List (the list of bookmarked ingredients on the left side of the screen)", "jei.config.client.bookmarkList.maxRows": "<PERSON>s", "jei.config.client.bookmarkList.maxRows.description": "Max number of rows shown.", "jei.config.client.bookmarkList.maxColumns": "<PERSON>", "jei.config.client.bookmarkList.maxColumns.description": "Max number of columns shown.", "jei.config.client.bookmarkList.horizontalAlignment": "Horizontal Alignment", "jei.config.client.bookmarkList.horizontalAlignment.description": "Horizontal alignment of the bookmark list inside the available area.", "jei.config.client.bookmarkList.verticalAlignment": "Vertical Alignment", "jei.config.client.bookmarkList.verticalAlignment.description": "Vertical alignment of the bookmark list inside the available area.", "jei.config.client.bookmarkList.buttonNavigationVisibility": "Navigation Visibility", "jei.config.client.bookmarkList.buttonNavigationVisibility.description": "Visibility of the top page buttons. Use AUTO_HIDE to only show it when there are multiple pages.", "jei.config.client.bookmarkList.drawBackground": "Draw GUI Background", "jei.config.client.bookmarkList.drawBackground.description": "Enable this to draw a background texture behind the bookmark list.", "jei.config.client.advanced.itemBlacklist": "Ingredient Blacklist", "jei.config.client.advanced.itemBlacklist.description": "List of ingredients that should not be displayed in the ingredient list overlay. Format: modId[:name[:meta]]. The hide ingredients mode will automatically add or remove entries here.", "jei.config.client.advanced.maxColumns": "<PERSON> Width", "jei.config.client.advanced.maxColumns.description": "The maximum width of the ingredient and bookmark list overlays.", "jei.config.modIdFormat.modName": "Mod Name", "jei.config.modIdFormat.modName.description": "Config options related to displaying Mod Names", "jei.config.modIdFormat.modName.modNameFormat": "Mod Name Format", "jei.config.modIdFormat.modName.modNameFormat.description": "Formatting for the mod names in tooltips for JEI GUIs. Leave blank to disable.", "jei.config.debug.debug": "Debug", "jei.config.debug.debug.description": "Config options to help developers debug issues in JEI", "jei.config.debug.debug.debugMode": "Debug Mode", "jei.config.debug.debug.debugMode.description": "Enable debug mode", "jei.config.debug.debug.debugGuis": "Debug GUIs", "jei.config.debug.debug.debugGuis.description": "Enable Debug GUIs mode", "jei.config.debug.debug.debugInputs": "Debug Inputs", "jei.config.debug.debug.debugInputs.description": "Enable Debug Inputs mode", "jei.config.debug.debug.debugInfoTooltipsEnabled": "Debug Info Tooltips", "jei.config.debug.debug.debugInfoTooltipsEnabled.description": "Add debug information to ingredient tooltips when advanced tooltips are enabled.", "jei.config.debug.debug.crashingTestItemsEnabled": "Enable Crashing Test Items", "jei.config.debug.debug.crashingTestItemsEnabled.description": "Adds ingredients to JEI that intentionally crash, to help debug JEI.", "jei.config.debug.debug.logSuffixTreeStats": "Log Search Tree Statistics", "jei.config.debug.debug.logSuffixTreeStats.description": "Log information about the suffix trees used for searching, to help debug JEI.", "jei.config.colors.colors": "Colors", "jei.config.colors.colors.description": "Config options related to searching for colors of items in JEI", "jei.config.colors.colors.searchColors": "Search Colors", "jei.config.colors.colors.searchColors.description": "Color values to search for.", "gui.jei.editMode.description": "JEI Hide Ingredients Mode:", "gui.jei.editMode.description.hide": "Press \"%s\" to hide.", "gui.jei.editMode.description.hide.wild": "Press \"%s\" to hide by wildcard.", "gui.jei.category.craftingTable": "Crafting", "gui.jei.category.stoneCutter": "Stonecutting", "gui.jei.category.smelting": "Smelting", "gui.jei.category.smoking": "Smoking", "gui.jei.category.blasting": "Blasting", "gui.jei.category.campfire": "Campfire Cooking", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.smelting_fuel": "Smelting Fuel", "gui.jei.category.smoking_fuel": "Smoking Fuel", "gui.jei.category.blasting_fuel": "Blasting Fuel", "gui.jei.category.fuel.smeltCount.single": "Smelts 1 item", "gui.jei.category.fuel.smeltCount": "Smelts %s items", "gui.jei.category.brewing": "Brewing", "gui.jei.category.brewing.steps": "Steps: %s", "gui.jei.category.compostable": "Composting", "gui.jei.category.compostable.chance": "Chance: %s%%", "gui.jei.category.grindstone": "Grinding", "gui.jei.category.grindstone.experience": "%s to %s XP", "gui.jei.category.itemInformation": "Information", "gui.jei.category.tagInformation": "%s Tags", "gui.jei.category.tagInformation.block": "Block Tags", "gui.jei.category.tagInformation.fluid": "Fluid Tags", "gui.jei.category.tagInformation.item": "Item <PERSON>s", "gui.jei.category.recipe.crashed": "This recipe crashed. Please see the client logs for details.", "jei.message.configured": "Install the \"Configured\" mod to access the in-game config", "jei.message.config.folder": "Or click here to open the JEI config folder instead", "jei.message.copy.recipe.id.success": "The following recipe ID was copied to the clipboard: %s", "jei.message.copy.recipe.id.failure": "Failed to copy the recipe ID to the clipboard because the ID is unknown", "jei.message.missing.recipes.from.server": "JEI is missing recipes. Please install JEI on the server to sync recipes to the client.\nSince Minecraft 1.21.2, recipes are stored on the server and not the client.", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "jei.key.shift": "SHIFT", "jei.key.mouse.left": "CLICK", "jei.key.mouse.right": "RIGHT-CLICK", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}