{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Innstillinger", "jei.tooltip.show.recipes": "<PERSON><PERSON>", "jei.tooltip.delete.item": "Klikk for å slette", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "<PERSON><PERSON><PERSON><PERSON>", "jei.tooltip.recipe.tag": "Kan bruke alle: %s", "jei.tooltip.item.colors": "Farger: %s", "jei.tooltip.shapeless.recipe": "Formløs Oppskrift", "jei.tooltip.error.recipe.transfer.missing": "Mangler Artikler å Overføre", "jei.tooltip.error.recipe.transfer.inventory.full": "Inventar er fullt", "key.jei.toggleOverlay": "Slå på overlegg med alle artikler", "key.jei.focusSearch": "Fokuser på søkeboksen", "key.jei.recipeBack": "<PERSON><PERSON>", "key.jei.showRecipe": "<PERSON><PERSON>", "key.jei.showRecipe2": "<PERSON><PERSON>", "key.jei.showUses": "<PERSON><PERSON>", "key.jei.showUses2": "<PERSON><PERSON>", "jei.config": "Innstillinger", "jei.config.default": "Standard", "jei.config.valid": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.title": "%MODNAME Innstillinger", "jei.config.mode": "Modus", "jei.config.mode.description": "<PERSON><PERSON> modusen JEI opererer i", "jei.config.mode.cheatItemsEnabled": "<PERSON><PERSON><PERSON>", "jei.config.mode.cheatItemsEnabled.description": "Gi ting i stedet for å vise oppskrift", "jei.config.mode.editEnabled": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.mode.editEnabled.description": "Skjul og gjør artikler synlig igjen i artikkel listen.", "jei.config.interface": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.interface.description": "Valg relatert til brukergrensesnittet.", "jei.config.interface.overlayEnabled": "Artikkel Liste Aktivert", "jei.config.interface.overlayEnabled.description": "Vis listen av artikler ved siden av GUI's som er åpne.", "jei.config.client.search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.client.search.description": "<PERSON>g <PERSON> til søkelinjen.", "jei.config.client.search.modNameSearchMode": "Krev @ for Mod Navn", "jei.config.client.search.modNameSearchMode.description": "Krev \"@\" foran et ord for å søke etter mod navn. Eksempel: \"@minecraft\" viser alle artikler fra Minecraft.", "jei.config.client.search.tooltipSearchMode": "Krev $ for Verktøytips", "jei.config.client.search.tooltipSearchMode.description": "Krev \"#\" foran et ord for å søke etter verktøytips. Eksempel: \"$C418\" viser artikler med C418 i verktøytipset.", "jei.config.client.search.creativeTabSearchMode": "Krev % for Kreativ Fane Navn", "jei.config.client.search.creativeTabSearchMode.description": "Krev \"%\" foran et ord for å søke etter kreative fane navn. Eksempel: \"%byggeblokker\" viser artikler fra den angitte fanen.", "jei.config.client.search.colorSearchMode": "Krev ^ for Farger", "jei.config.client.search.colorSearchMode.description": "<PERSON>re<PERSON> \"^\" foran et ord for å søke på artikkel med farger. Eksempel: \"^blå\" viser artikler som er blå.", "jei.config.client.advanced": "Avansert", "jei.config.client.advanced.description": "Avanserte innstillinger for å endre måten JEI fungerer på.", "jei.config.client.advanced.itemBlacklist": "Svartliste for Artikler", "jei.config.client.advanced.itemBlacklist.description": "Liste over artikler som ikke skal vises i artikkel listen. Format: modId[:navn[:meta]]. Redigerings Modus vil automatisk legge til eller fjerne oppføringer her.", "jei.config.debug.debug.debugMode": "Feilsøkings Modus", "jei.config.debug.debug.debugMode.description": "N<PERSON><PERSON>g for utviklerne av JEI. Legger til tusenvis av test artikler og noen feilsøkings oppskrifter.", "gui.jei.editMode.description": "JEI Artikkel Liste Redigerings Modus:", "gui.jei.editMode.description.hide": "%s for å skjule.", "gui.jei.editMode.description.hide.wild": "%s for å skjule med jokertegn(*).", "gui.jei.category.craftingTable": "Arbeidsbenk", "gui.jei.category.smelting": "Smelting", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.fuel": "<PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.brewing": "<PERSON><PERSON><PERSON>", "gui.jei.category.brewing.steps": "Steg: %s", "gui.jei.category.itemInformation": "Beskrivelse", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}