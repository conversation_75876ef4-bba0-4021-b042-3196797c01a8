{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "JEI הגדרות", "jei.tooltip.show.recipes": "הצג מתכונים", "jei.tooltip.delete.item": "לחץ כדי למחוק", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "הזז חפצים", "jei.tooltip.recipe.tag": "קבל כל: %s", "jei.tooltip.item.colors": "צבעים: %s", "jei.tooltip.shapeless.recipe": "מת<PERSON><PERSON>ן חסר צורה", "jei.tooltip.cheat.mode.button.enabled": "מצב צ'יטים", "jei.tooltip.error.recipe.transfer.missing": "חפצים חסרים", "jei.tooltip.error.recipe.transfer.inventory.full": "המלאי מלא מידי", "jei.tooltip.error.recipe.transfer.no.server": "חייב שלשרת יהיה JEI מותקן", "jei.tooltip.error.crash": "בעיה בToolTip יש לבדוק את הלוג", "key.jei.toggleOverlay": "הפעל/הפסק כיסוי רשימת חפצים", "key.jei.focusSearch": "בחר <PERSON><PERSON> חי<PERSON>וש", "key.jei.recipeBack": "הצג עמוד חפצים קודם", "key.jei.toggleCheatMode": "הפעל.הפסק מצב רמאות חפצים", "key.jei.showRecipe": "הצג מתכונים לחפצים", "key.jei.showRecipe2": "הצג מתכונים לחפצים", "key.jei.showUses": "הצג שימוש של חפצים", "key.jei.showUses2": "הצג שימוש של חפצים", "jei.config": "JEI הגדרות", "jei.config.default": "ברירת מחדל", "jei.config.valid": "תקף", "jei.config.title": "%MODNAME הגדרות", "jei.config.mode": "מצב", "jei.config.mode.description": "שנה את המצב שJEI עובד בו", "jei.config.mode.cheatItemsEnabled": "מצב רמאות חפצים", "jei.config.mode.cheatItemsEnabled.description": "תן חפצים במקום להציר מתכונים", "jei.config.mode.editEnabled": "מצב החבאת חפצים", "jei.config.mode.editEnabled.description": "החבא והצג חפצים על ידי לחיצה עליהם ברשימת החפצים.", "jei.config.interface": "<PERSON><PERSON><PERSON><PERSON>", "jei.config.interface.description": "הגדרות בנוגע לממשק המשתמש", "jei.config.interface.overlayEnabled": "אפשר רשימת חפצים", "jei.config.interface.overlayEnabled.description": "הצג את הרשימה של החפצים ליד הממשקים הגרפיים הפתוחים", "jei.config.client.search": "הגדרות חיפוש", "jei.config.client.search.description": "הגדרות הקשורות לבר החיפוש", "jei.config.client.search.modNameSearchMode": "נידרש @ בשביל שם המוד", "jei.config.client.search.modNameSearchMode.description": "נידרש \"@\" לפני מילה כדי לחפש לפי שם המוד.", "jei.config.client.search.tooltipSearchMode": "נידרש $ בשביל Tooltip", "jei.config.client.search.tooltipSearchMode.description": "נידרש \"$\" לפני מילה כדי לחפש Tooltip", "jei.config.client.search.creativeTabSearchMode": "נידרש %% בשביל שמות של טאבים ממצב יצירתי", "jei.config.client.search.creativeTabSearchMode.description": "נדרש \"%\" לפני מילה כדי לחפש שמות של טאבים ממצב יצירתי.", "jei.config.client.search.colorSearchMode": "נדרש ^ בשביל צבעים", "jei.config.client.search.colorSearchMode.description": "נדרש \"^\" לפני מילה גדי לחפש צבע של חפץ.", "jei.config.client.advanced": "מתקדם", "jei.config.client.advanced.description": "הגדרות מתכדמות כדי לשנות את האופן שJEI מתנהג.", "jei.config.client.advanced.itemBlacklist": "רשימה שחורה של חפצים", "jei.config.client.advanced.itemBlacklist.description": "רישמה של חפצים שלא יוצגו ברשימת החפצים. פורמט: modId[:name[:meta]]. מצב עריכה אוטומטית יוסיף או יוריד דברים מפה.", "jei.config.debug.debug.debugMode": "מצ<PERSON> דיבאג", "jei.config.debug.debug.debugMode.description": "שימושי רק בישביל מפתחי JEI, מוסיף אלפי חפצי ניסיון וכמה מתכוני דיבאג.", "jei.config.client.appearance.centerSearch": "בר חי<PERSON>וש מרכזי", "jei.config.client.appearance.centerSearch.description": "הזז את בר החיפוש של JEI לתחתית מרכז המסך.", "gui.jei.editMode.description": "JEI מצב עריכת רשימת חפצים:", "gui.jei.category.craftingTable": "בניה", "gui.jei.category.smelting": "התכה", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.fuel": "<PERSON><PERSON><PERSON>", "gui.jei.category.fuel.smeltCount.single": "התח חפץ אחד", "gui.jei.category.fuel.smeltCount": "התח %s חפצים", "gui.jei.category.brewing": "מב<PERSON>ל", "gui.jei.category.brewing.steps": "צעדים: %s", "gui.jei.category.itemInformation": "תיאור", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}