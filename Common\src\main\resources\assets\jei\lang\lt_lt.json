{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Konfigūracija", "jei.tooltip.show.recipes": "<PERSON><PERSON><PERSON> receptus", "jei.tooltip.delete.item": "Spustelė<PERSON> nor<PERSON>", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "<PERSON><PERSON><PERSON>", "jei.tooltip.recipe.tag": "Priima bet ką: %s", "jei.tooltip.item.colors": "Spalvos: %s", "jei.tooltip.shapeless.recipe": "Be<PERSON><PERSON> receptas", "jei.tooltip.cheat.mode.button.enabled": "Cheat rėž<PERSON>s", "jei.tooltip.error.recipe.transfer.missing": "Trūkstami da<PERSON>", "jei.tooltip.error.recipe.transfer.inventory.full": "Inventorius per pilnas", "key.jei.focusSearch": "Pasirinkti paieškos juo<PERSON>ą", "key.jei.recipeBack": "<PERSON><PERSON><PERSON> buvusio recepto puslapį", "key.jei.showRecipe": "<PERSON><PERSON><PERSON> da<PERSON> recept<PERSON>", "key.jei.showRecipe2": "<PERSON><PERSON><PERSON> da<PERSON> recept<PERSON>", "key.jei.showUses": "<PERSON><PERSON><PERSON> da<PERSON>", "key.jei.showUses2": "<PERSON><PERSON><PERSON> da<PERSON>", "jei.config": "Konfigūracija", "jei.config.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.config.valid": "<PERSON><PERSON><PERSON>", "jei.config.title": "%MODNAME konfigūracija", "jei.config.mode": "Rėžimas", "jei.config.client.search": "Paieškos nustatymai", "jei.config.client.search.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, susiję su paieškos juosta.", "jei.config.client.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.fuel": "<PERSON><PERSON>", "gui.jei.category.brewing.steps": "Žingsniai: %s", "gui.jei.category.itemInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}