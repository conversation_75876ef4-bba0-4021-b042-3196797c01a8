package mezz.jei.api.recipe.advanced;

import mezz.jei.api.recipe.IFocus;
import mezz.jei.api.recipe.IRecipeManager;
import mezz.jei.api.recipe.category.IRecipeCategory;
import mezz.jei.api.recipe.types.IRecipeType;
import mezz.jei.api.registration.IAdvancedRegistration;

import java.util.List;

/**
 * {@link IRecipeManagerPlugin}s are used by the {@link IRecipeManager} to look up recipes.
 *
 * Recipes can be generated by this plugin dynamically in response to players looking up recipes.
 * This is useful when your mod has dynamic recipes with too many inputs or outputs to create normal recipes.
 *
 * JEI has its own internal plugin, which uses information from {@link IRecipeCategory} to look up recipes.
 * Implementing your own Recipe Registry Plugin offers total control of lookups, but it must be fast.
 *
 * Add your plugin with {@link IAdvancedRegistration#addRecipeManagerPlugin(IRecipeManagerPlugin)}.
 *
 * For a simpler version that handles only one recipe type, use {@link ISimpleRecipeManagerPlugin}.
 *
 * Get help with the implementation using {@link IRecipeManagerPluginHelper}.
 */
public interface IRecipeManagerPlugin {
	/**
	 * Returns a list of Recipe Types offered for the focus.
	 *
	 * @since 9.5.0
	 */
	<V> List<IRecipeType<?>> getRecipeTypes(IFocus<V> focus);

	/**
	 * Returns a list of Recipes with the recipe type that have the focus.
	 * This is used internally by JEI to implement {@link IRecipeManager#createRecipeLookup(IRecipeType)}.
	 */
	<T, V> List<T> getRecipes(IRecipeType<T> recipeType, IFocus<V> focus);

	/**
	 * Returns a list of all Recipes with the {@link IRecipeType}.
	 * This is used internally by JEI to implement {@link IRecipeManager#createRecipeLookup(IRecipeType)}.
	 *
	 * @since 20.0.0
	 */
	<T> List<T> getRecipes(IRecipeType<T> recipeType);
}
