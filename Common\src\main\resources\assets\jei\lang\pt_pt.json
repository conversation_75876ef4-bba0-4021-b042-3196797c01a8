{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Configuração do JEI", "jei.tooltip.show.recipes": "Mostrar <PERSON>", "jei.tooltip.show.all.recipes.hotkey": "%s para mostrar todas as Receitas", "jei.tooltip.delete.item": "Clicar para Apagar", "jei.tooltip.liquid.flowing": "%s (fluindo)", "jei.tooltip.transfer": "Mover <PERSON><PERSON>", "jei.tooltip.recipe.tag": "Aceita etiqueta: %s", "jei.tooltip.item.colors": "Cores: %s", "jei.tooltip.item.search.aliases": "Pesquisar Aliases:", "jei.tooltip.shapeless.recipe": "Receita sem Forma", "jei.tooltip.cheat.mode.button.enabled": "Modo Batota ativado", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Pressionar \"%s\" para alterná-lo entre ativo/inativo.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s para alterná-lo entre ativo/inativo.", "jei.tooltip.recipe.by": "Receita por: %s", "jei.tooltip.recipe.id": "ID da Receita: %s", "jei.tooltip.not.enough.space": "Não há espaço suficiente para exibir a sobreposição da lista de ingredientes JEI aqui.", "jei.tooltip.ingredient.list.disabled": "As sobreposições JEI estão desativadas.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Pressionar \"%s\" para ativá-las.", "jei.tooltip.bookmarks": "Marcadores JEI", "jei.tooltip.bookmarks.usage.nokey": "Adicionar um atalho de teclado para marcadores JEI nas Configurações de controlos.", "jei.tooltip.bookmarks.usage.key": "Passar o rato sobre um ingrediente e pressionar \"%s\" para marcá-lo.", "jei.tooltip.bookmarks.not.enough.space": "Não há espaço suficiente para exibir a sobreposição da lista de favoritos JEI aqui.", "jei.tooltip.bookmarks.recipe": "%s <PERSON><PERSON> da Receita", "jei.tooltip.bookmarks.recipe.add": "Marcar esta receita.", "jei.tooltip.bookmarks.recipe.remove": "Remover o marcador desta receita.", "jei.tooltip.bookmarks.tooltips.usage": "[%s para mostrar detalhes]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[%s para criar um]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[%s para criar muitos]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "Mostrar receitas marcadas primeiro (ativado)", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "Mostrar receitas marcadas primeiro (desativado)", "jei.tooltip.recipe.sort.craftable.first.enabled": "Mostrar receitas que podem ser criadas primeiro (ativado)", "jei.tooltip.recipe.sort.craftable.first.disabled": "Mostrar receitas que podem ser criadas primeiro (desativado)", "jei.key.category.overlays": "JEI (Sobreposições)", "key.jei.toggleOverlay": "Mostrar/Esconder JEI Sobreposições", "key.jei.focusSearch": "Selecionar Barra de Pesquisa", "key.jei.previousPage": "Página Anterior", "key.jei.nextPage": "Próxima <PERSON>", "key.jei.toggleBookmarkOverlay": "Mostrar/Esconder Ingredientes Marcados", "jei.key.category.recipe.gui": "JEI (Receitas)", "key.jei.recipeBack": "<PERSON><PERSON>ita <PERSON>", "key.jei.previousCategory": "Categoria da Receita Anterior", "key.jei.nextCategory": "Categoria da Próxima Receita", "key.jei.previousRecipePage": "Página da Receita Anterior", "key.jei.nextRecipePage": "Página da Próxima Receita", "key.jei.closeRecipeGui": "Fechar GUI das Receitas", "jei.key.category.mouse.hover": "JEI (Passar com o Rato)", "key.jei.bookmark": "Adicionar/Remover Marcador", "key.jei.showRecipe": "<PERSON><PERSON>", "key.jei.showRecipe2": "<PERSON><PERSON>", "key.jei.showUses": "Mostrar Utilizações", "key.jei.showUses2": "Mostrar Utilizações", "key.jei.transferRecipeBookmark": "Criar <PERSON><PERSON> (1 vez)", "key.jei.maxTransferRecipeBookmark": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (valor máximo)", "jei.key.category.search": "JEI (Filtro de Pesquisa)", "key.jei.clearSearchBar": "Limpar <PERSON> de Pesquisa", "key.jei.previousSearch": "Pesquisa Anterior", "key.jei.nextSearch": "Próxima <PERSON>", "gui.jei.category.craftingTable": "Criação", "gui.jei.category.stoneCutter": "Corte de pedra", "gui.jei.category.smelting": "Fundição", "gui.jei.category.smoking": "<PERSON><PERSON><PERSON>", "gui.jei.category.blasting": "Explosão", "gui.jei.category.campfire": "Cozinhar na fogueira", "gui.jei.category.fuel": "Combustível", "gui.jei.category.fuel.smeltCount.single": "Funde 1 item", "gui.jei.category.fuel.smeltCount": "Funde %s itens", "gui.jei.category.brewing": "Fermentação", "gui.jei.category.brewing.steps": "Passos: %s", "gui.jei.category.compostable": "Compostável", "gui.jei.category.compostable.chance": "Probabilidade: %s%%", "gui.jei.category.itemInformation": "Informação", "gui.jei.category.tagInformation": "%s Etiquetas", "gui.jei.category.tagInformation.block": "<PERSON><PERSON>", "gui.jei.category.tagInformation.fluid": "<PERSON><PERSON><PERSON><PERSON>", "gui.jei.category.tagInformation.item": "<PERSON><PERSON>", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}