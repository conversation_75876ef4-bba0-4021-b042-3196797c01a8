{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Configurazione della JEI", "jei.tooltip.show.recipes": "Mostra ricette", "jei.tooltip.delete.item": "Clicca per eliminare", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "<PERSON><PERSON><PERSON> oggetti", "jei.tooltip.recipe.tag": "Accetta ogni: %s", "jei.tooltip.item.colors": "Colori: %s", "jei.tooltip.shapeless.recipe": "Ricetta senza forma", "jei.tooltip.cheat.mode.button.enabled": "Modalità trucchi attivata", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "Premi %s per attivarla/disattivarla.", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s qui per attivarla/disattivarla.", "jei.tooltip.recipe.by": "Ricetta di: %s", "jei.tooltip.recipe.id": "ID ricetta: %s", "jei.tooltip.not.enough.space": "Non c'è abbastanza spazio per la schermata JEI qui.", "jei.tooltip.ingredient.list.disabled": "La sovrapposizione della JEI è disabilitata.", "jei.tooltip.ingredient.list.disabled.how.to.fix": "Premi %s per abilitarla.", "jei.tooltip.bookmarks": "Segnalibri JEI", "jei.tooltip.bookmarks.usage.nokey": "Associa un tasto per i segnalibri JEI nelle tue inpostazioni dei comandi.", "jei.tooltip.bookmarks.usage.key": "Passa il mouse su un ingrediente e premi \"%s\" per aggiungerlo ai segnalibri.", "jei.tooltip.bookmarks.not.enough.space": "Non c'è abbastanza spazio per la schermata dei segnalibri JEI qui.", "jei.tooltip.error.recipe.transfer.missing": "<PERSON><PERSON><PERSON> mancanti", "jei.tooltip.error.recipe.transfer.inventory.full": "L'inventario è pieno", "jei.tooltip.error.recipe.transfer.no.server": "Nel server deve essere installata la JEI", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "La ricetta è troppo grande per essere fatta in una griglia 2x2.", "jei.tooltip.error.crash": "Errore suggerimento, guarda i log", "jei.chat.error.no.cheat.permission.1": "Non hai il permesso per abilitare la modalità trucchi della JEI.", "jei.chat.error.no.cheat.permission.disabled": "Su questo server, la modalità trucchi della JEI è disabilitata per tutti i giocatori.", "jei.chat.error.no.cheat.permission.enabled": "Su questo server, i seguenti tipi di giocatori possono usare la modalità trucchi della JEI:", "jei.chat.error.no.cheat.permission.creative": "giocatori che sono in modalità Creativa", "jei.chat.error.no.cheat.permission.op": "giocatori che hanno i permessi di Operatore (/op)", "jei.chat.error.no.cheat.permission.give": "giocatori che possono usare /give", "jei.key.category.overlays": "JEI (Sovraimpressione)", "key.jei.toggleOverlay": "Mostra/nascondi JEI", "key.jei.focusSearch": "Seleziona la barra di ricerca", "key.jei.previousPage": "Mostra la pagina precedente", "key.jei.nextPage": "Mostra la pagina successiva", "key.jei.toggleBookmarkOverlay": "Mostra/nascondi ingredienti aggiunto ai segnalibri", "jei.key.category.recipe.gui": "JEI (Ricette)", "key.jei.recipeBack": "<PERSON>ra ricetta precedente", "key.jei.previousCategory": "Mostra categoria ricetta precedente", "key.jei.nextCategory": "Mostra categoria ricetta successiva", "key.jei.previousRecipePage": "<PERSON><PERSON><PERSON>e", "key.jei.nextRecipePage": "Pagina Ricette successiva", "jei.key.category.cheat.mode": "JEI (Modalità Trucchi)", "key.jei.toggleCheatMode": "Disabilitare/abilitare la modalità trucchi", "key.jei.cheatOneItem": "Ottieni 1 oggetto", "key.jei.cheatOneItem2": "Ottieni 1 oggetto", "key.jei.cheatItemStack": "Ottieni 1 stack", "key.jei.cheatItemStack2": "Ottieni 1 stack", "jei.key.category.hover.config.button": "JEI (Tenendo il mouse sopra il pulsante di configurazione)", "key.jei.toggleCheatModeConfigButton": "Attiva/Disattiva modalità trucchi", "jei.key.category.edit.mode": "JEI (Modalità Modifica)", "key.jei.toggleEditMode": "Attiva/Disattiva Modalità Nascondi Ingredienti", "key.jei.toggleHideIngredient": "Nascondi ingrediente", "key.jei.toggleWildcardHideIngredient": "Nascondi ingrediente (wildcard)", "jei.key.category.mouse.hover": "JEI (Tenendo il mouse sopra)", "key.jei.bookmark": "Agg<PERSON>ng<PERSON>/rimuovi ai segnalibri", "key.jei.showRecipe": "<PERSON>ra <PERSON>", "key.jei.showRecipe2": "<PERSON>ra <PERSON>", "key.jei.showUses": "<PERSON><PERSON>", "key.jei.showUses2": "<PERSON><PERSON>", "jei.key.category.search": "JEI (Filtri di Ricerca)", "key.jei.clearSearchBar": "Cancella i Filtri di Ricerca", "key.jei.previousSearch": "Ricerca Precedente", "key.jei.nextSearch": "Ricerca Successiva", "jei.key.category.dev.tools": "JEI (Strumenti degli Sviluppatori)", "key.jei.copy.recipe.id": "Copia l'ID ricetta nella Clipboard", "jei.config": "Configurazione della JEI", "jei.config.default": "Predefinito", "jei.config.valid": "Valido", "jei.config.title": "Configurazione %MODNAME", "jei.config.mode": "Modalità", "jei.config.mode.description": "Cambia la modalità nella quale la JEI opera.", "jei.config.mode.cheatItemsEnabled": "Modalità trucchi", "jei.config.mode.cheatItemsEnabled.description": "Dai gli oggetti invece di mostrarne la ricetta.", "jei.config.mode.editEnabled": "Modalità nascondi ingredienti", "jei.config.mode.editEnabled.description": "Nasconde e mostra gli ingredienti cliccandoli sulla lista.", "jei.config.interface": "Interfaccia", "jei.config.interface.description": "Opzioni riguardanti l'interfaccia utente.", "jei.config.interface.overlayEnabled": "Mostra lista ingredienti", "jei.config.interface.overlayEnabled.description": "Mostra la lista dei ingredienti a fianco delle GUI aperte.", "jei.config.interface.bookmarkOverlayEnabled": "<PERSON>ra segnalibri", "jei.config.interface.bookmarkOverlayEnabled.description": "Mostra la lista dei segnalibri a fianco delle GUI aperte.", "jei.config.client.search": "Opzioni di ricerca", "jei.config.client.search.description": "Opzioni che riguardano la barra di ricerca.", "jei.config.client.search.modNameSearchMode": "@ModName", "jei.config.client.search.modNameSearchMode.description": "Modalità di ricerca per nomi delle mod (prefisso: @)", "jei.config.client.search.tooltipSearchMode": "$Tooltip", "jei.config.client.search.tooltipSearchMode.description": "Modalità di ricerca per suggerimento (prefisso: $)", "jei.config.client.search.tagSearchMode": "#Tag", "jei.config.client.search.tagSearchMode.description": "Modalità di ricerca per Tag (prefisso: #)", "jei.config.client.search.creativeTabSearchMode": "%%CreativeModeTab", "jei.config.client.search.creativeTabSearchMode.description": "Modalità di ricerca per i nomi della schermata creativa (prefisso: %)", "jei.config.client.search.colorSearchMode": "^Color", "jei.config.client.search.colorSearchMode.description": "Modalità di ricerca per colori (prefisso: ^)", "jei.config.client.search.resourceLocationSearchMode": "&ResourceLocation", "jei.config.client.search.resourceLocationSearchMode.description": "Modalità di ricerca per ID delle risorse (prefisso: &)", "jei.config.client.search.searchAdvancedTooltips": "Ricerca suggerimenti avanzati", "jei.config.client.advanced": "Avanzate", "jei.config.client.advanced.description": "Opzioni avanzate per cambiare il modo in cui funziona la JEI.", "jei.config.client.advanced.itemBlacklist": "Lista nera dei oggetti", "jei.config.client.advanced.itemBlacklist.description": "Lista dei oggetti che non devono essere mostrati nella lista. Formato: modId[:name[:meta]]. La modalità nascondi Ingredienti aggiunge e rimuove automaticamente questi oggetti.", "jei.config.debug.debug.debugMode": "Modalità debug", "jei.config.debug.debug.debugMode.description": "Serve solo agli sviluppatori della JEI, aggiunge dei ingredienti per i test e qualche ricetta di debug.", "jei.config.client.appearance.centerSearch": "Centra la barra di ricerca", "jei.config.client.appearance.centerSearch.description": "Sposta la barra di ricerca della JEI in basso al centro dello schermo.", "jei.config.modIdFormat.modName.modNameFormat": "Formato nome della mod", "jei.config.modIdFormat.modName.modNameFormat.description": "Come il nome della mod dovrebbe essere formattato nel suggerimento per le gui della JEI. Lascia vuoto per disabilitare.", "jei.config.client.advanced.maxColumns": "Larghezza di sovrapposizione massima", "jei.config.client.advanced.maxColumns.description": "Larghezza massima della lista degli ingredienti.", "jei.config.client.appearance.recipeGuiHeight": "Altezza massima della GUI ricette", "jei.config.client.appearance.recipeGuiHeight.description": "L'altezza massima della GUI delle ricette.", "jei.config.client.cheating.giveMode": "Modalità dare", "jei.config.client.cheating.giveMode.description": "Sc<PERSON>li se la JEI dovrebbe dare gli ingredienti direttamente nell'inventario o prenderli con il mouse.", "gui.jei.editMode.description": "Modalità nascondi ingredienti nella JEI:", "gui.jei.editMode.description.hide": "%s per nascondere.", "gui.jei.editMode.description.hide.wild": "%s per nascondere con carattere jolly.", "gui.jei.category.craftingTable": "Fabbricazione", "gui.jei.category.stoneCutter": "Lavorazione della pietra", "gui.jei.category.smelting": "Fusione", "gui.jei.category.smoking": "Affumicazione", "gui.jei.category.blasting": "Sabbiatura", "gui.jei.category.campfire": "Cottura con Fuoco da campo", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.fuel": "Carburante", "gui.jei.category.fuel.smeltCount.single": "Cucina 1 oggetto", "gui.jei.category.fuel.smeltCount": "Cucina %s og<PERSON>ti", "gui.jei.category.brewing": "Alchimia", "gui.jei.category.brewing.steps": "Passi: %s", "gui.jei.category.compostable": "Compostabile", "gui.jei.category.compostable.chance": "Probabilità: %s%%", "gui.jei.category.itemInformation": "Descrizione", "jei.message.configured": "Installa la mod \"Configured\" per accedere alle configurazioni di gioco", "jei.message.copy.recipe.id.success": "ID Ricetta Copiato nella clipboard: %s", "jei.message.copy.recipe.id.failure": "Impossibile Copiare l'ID Ricetta nella clipboard, l'ID Ricetta è sconosciuto", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}