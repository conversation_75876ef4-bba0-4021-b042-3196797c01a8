package mezz.jei.api.recipe.advanced;

import mezz.jei.api.ingredients.ITypedIngredient;
import mezz.jei.api.registration.IAdvancedRegistration;

import java.util.List;

/**
 * A simpler version of {@link IRecipeManagerPlugin} that only handles one type of recipe.
 * Unlike the more complex {@link IRecipeManagerPlugin}, catalysts are handled automatically,
 * and the interface is streamlined to handle only one type.
 *
 * Recipes can be generated by this plugin dynamically in response to players looking up recipes.
 * This is useful when your mod has dynamic recipes with too many inputs or outputs to create normal recipes.
 *
 * Register your plugin using {@link IAdvancedRegistration#addSimpleRecipeManagerPlugin}.
 *
 * @since 19.16.0
 */
public interface ISimpleRecipeManagerPlugin<T> {
	/**
	 * @return true if this ingredient is an input to a recipe generated by this plugin.
	 *
	 * @since 19.16.0
	 */
	boolean isHandledInput(ITypedIngredient<?> input);

	/**
	 * @return true if this ingredient is an output to a recipe generated by this plugin.
	 *
	 * @since 19.16.0
	 */
	boolean isHandledOutput(ITypedIngredient<?> output);

	/**
	 * @return all the recipes that have this ingredient as an input.
	 *
	 * @since 19.16.0
	 */
	List<T> getRecipesForInput(ITypedIngredient<?> input);

	/**
	 * @return all the recipes that have this ingredient as an output.
	 *
	 * @since 19.16.0
	 */
	List<T> getRecipesForOutput(ITypedIngredient<?> output);

	/**
	 * @return some recipes that can be generated by this plugin, to show as an example
	 * when players are browsing all recipes.
	 *
	 * @since 19.16.0
	 */
	List<T> getAllRecipes();
}
