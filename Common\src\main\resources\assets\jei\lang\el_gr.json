{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "Ρυθμίσεις JEI", "jei.tooltip.show.recipes": "Δείξε συνταγές", "jei.tooltip.delete.item": "Πάτα για Διαγραφή", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.transfer": "Μετακίνησε αντικείμενα", "jei.tooltip.recipe.tag": "Δέχεται: %s", "jei.tooltip.item.colors": "Χρώματα: %s", "jei.tooltip.shapeless.recipe": "Άμορφη Συνταγή", "jei.tooltip.cheat.mode.button.enabled": "Λειτουργ<PERSON><PERSON> Cheat", "jei.tooltip.error.recipe.transfer.missing": "Ελλιπές Αντικείμενα", "jei.tooltip.error.recipe.transfer.inventory.full": "Το inventory σου είναι υπερβολικά γεμάτο", "jei.tooltip.error.recipe.transfer.no.server": "Ο σερβερ πρέπει να έχει εγκατεστημένο το JEI", "jei.tooltip.error.crash": "Πρόβλημα Επεξήγησης, δες την καταγραφή παιχνιδιού", "key.jei.toggleOverlay": "Ενάλλαξε την Επικάληψη του Καταλόγου Αντικειμένων", "key.jei.focusSearch": "Διάλεξε Μπάρα Αναζήτησης", "key.jei.recipeBack": "Δείξε Προηγούμενη Σελίδα Συνταγών", "key.jei.toggleCheatMode": "Ενάλλαξε Λειτουργ<PERSON><PERSON> Cheat Αντικειμένων", "key.jei.showRecipe": "Δείξε Συντα<PERSON><PERSON> Αντικειμένου", "key.jei.showRecipe2": "Δείξε Συντα<PERSON><PERSON> Αντικειμένου", "key.jei.showUses": "Δείξε Χρήσ<PERSON>ις Αντικειμένου", "key.jei.showUses2": "Δείξε Χρήσ<PERSON>ις Αντικειμένου", "jei.config": "Ρυθμίσεις JEI", "jei.config.default": "Αρχικό", "jei.config.valid": "Έγκυρο", "jei.config.title": "Ρυθμίσεις %MODNAME", "jei.config.mode": "Λειτουργία", "jei.config.mode.description": "Άλλαξε την λειτουργία στην οποία το JEI λειτουργεί.", "jei.config.mode.cheatItemsEnabled": "Λειτουρ<PERSON><PERSON><PERSON> Cheat Αντικειμένων", "jei.config.mode.cheatItemsEnabled.description": "Δώσε αντικείμενα αντί να δείχνει την συνταγή.", "jei.config.mode.editEnabled": "Λειτουρ<PERSON><PERSON><PERSON> Εξαφάνισης Αντικειμένων", "jei.config.mode.editEnabled.description": "Εμφάνισε και εξαφάνισε αντικείμενα κάνοντας κλικ σε αυτά στην λίστα αντικειμένων.", "jei.config.interface": "Διεπαφή", "jei.config.interface.description": "Ρυθμίσεις με σχέση την Διεπαφή του Χρήστη.", "jei.config.interface.overlayEnabled": "Λίστα Αντικειμένων Ενεργοποιημένη", "jei.config.interface.overlayEnabled.description": "Δείξε την λίστα αντικειμένων δίπλα από αλλα GUIs.", "jei.config.client.search": "Ρυθμίσεις Αναζήτησης", "jei.config.client.search.description": "Ρυθμίσεις με σχέση την Μπάρα Αναζήτησης.", "jei.config.client.search.modNameSearchMode": "Απαίτηση @ για Όνομα Mod", "jei.config.client.search.modNameSearchMode.description": "Απαίτηση \"@\" μπροστά από μια λέξη για αναζήτηση από το όνομα του mod.", "jei.config.client.search.tooltipSearchMode": "Απαίτηση $ για Πληροφορίες Αντικειμένου", "jei.config.client.search.tooltipSearchMode.description": "Απαίτηση \"$\" μπροστά από μια λέξη για αναζήτηση από τις πληροφορίες των αντικειμένων.", "jei.config.client.search.creativeTabSearchMode": "Απαίτηση %% για όνομα Creative Tab", "jei.config.client.search.creativeTabSearchMode.description": "Απαίτηση \"%\" μπρο<PERSON>τά από μια λέξη για αναζήτηση από τα ονόματα των creative tab.", "jei.config.client.search.colorSearchMode": "Απαίτηση ^ για Χρώματα", "jei.config.client.search.colorSearchMode.description": "Απαίτηση \"^\" μπρο<PERSON><PERSON><PERSON> από μια λέξη για αναζήτηση από χρώματα αντικειμένων.", "jei.config.client.advanced": "Προχωρημένα", "jei.config.client.advanced.description": "Προχωρημένες ρυθμίσεις για να αλλάξεις τον τρόπο που λειτουργεί το JEI.", "jei.config.client.advanced.itemBlacklist": "Μαύρη Λίστα Αντικειμένων", "jei.config.client.advanced.itemBlacklist.description": "Λίστα αντικειμένων τα οποία δεν πρέπει να εμφανίζονται στην λίστα αντικειμένων. Μορφή: modId[:name[:meta]]. Η Λειτουργία Επεξεργασίας βάζει και διαγράφει αυτόματα αντικείμενα εδώ.", "jei.config.debug.debug.debugMode": "Λειτουργ<PERSON>α Debug", "jei.config.debug.debug.debugMode.description": "Μόνο χρήσιμο για προγραμματιστές του JEI, βάζει χιλιάδες δοκιμαστικά αντικείμενα και συνταγές.", "jei.config.client.appearance.centerSearch": "Κέντραρε Μπάρα Αναζήτησης", "jei.config.client.appearance.centerSearch.description": "Μετακ<PERSON>νησε την JEI μπάρα αναζήτης στο κεντρικό κάτω μέρος της οθόνης.", "gui.jei.editMode.description": "JEI Λίστα Αντικειμένων Λειτουργία Επεξεργασίας:", "gui.jei.editMode.description.hide": "%s για να εξαφανίσεις.", "gui.jei.editMode.description.hide.wild": "%s για εξαφάνιση από wildcard.", "gui.jei.category.craftingTable": "Χειροτεχνία", "gui.jei.category.smelting": "Ψήσιμο", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.fuel": "Καύσιμο", "gui.jei.category.fuel.smeltCount.single": "Ψήνει 1 αντικείμενο", "gui.jei.category.fuel.smeltCount": "Ψήνει %s αντικείμενα", "gui.jei.category.brewing": "Ζύμωση", "gui.jei.category.brewing.steps": "Βήματα: %s", "gui.jei.category.itemInformation": "Περιγραφή", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}