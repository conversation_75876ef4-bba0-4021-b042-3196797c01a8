{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "JEI 配置", "jei.tooltip.show.recipes": "显示配方", "jei.tooltip.show.all.recipes.hotkey": "按下 %s 以显示所有配方。", "jei.tooltip.delete.item": "单击以删除。", "jei.tooltip.liquid.amount.with.capacity": "%s / %s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.liquid.flowing": "%s （流动中）", "jei.tooltip.transfer": "转移物品", "jei.tooltip.recipe.tag": "接受下列任何物品：%s", "jei.tooltip.item.colors": "颜色：%s", "jei.tooltip.item.search.aliases": "搜索别名：", "jei.tooltip.shapeless.recipe": "无序配方", "jei.tooltip.cheat.mode.button.enabled": "作弊模式启用。", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "按 %s 以切换。", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "%s 此处以切换。", "jei.tooltip.recipe.by": "该配方由 %s 添加。", "jei.tooltip.recipe.id": "配方 ID：%s。", "jei.tooltip.not.enough.space": "屏幕空间不足，无法显示 JEI 界面。", "jei.tooltip.ingredient.list.disabled": "JEI 悬浮界面已禁用。", "jei.tooltip.ingredient.list.disabled.how.to.fix": "按 %s 以启用。", "jei.tooltip.bookmarks": "JEI 书签", "jei.tooltip.bookmarks.usage.nokey": "在按键设置中为 JEI 书签添加快捷键。", "jei.tooltip.bookmarks.usage.key": "将鼠标悬停在某个物品上，然后按 %s 将其添加为书签。", "jei.tooltip.bookmarks.not.enough.space": "这里没有足够的空间来显示书签。", "jei.tooltip.bookmarks.recipe": "%s 合成表书签", "jei.tooltip.bookmarks.recipe.add": "添加该配方到书签。", "jei.tooltip.bookmarks.recipe.remove": "从书签中移除该配方。", "jei.tooltip.bookmarks.tooltips.usage": "[%s 显示细节]", "jei.tooltip.bookmarks.tooltips.transfer.usage": "[%s 一次合成]", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "[%s 大量合成]", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "优先显示书签配方（启用）。", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "优先显示书签配方（禁用）。", "jei.tooltip.recipe.sort.craftable.first.enabled": "优先显示可合成的配方（启用）。", "jei.tooltip.recipe.sort.craftable.first.disabled": "优先显示可合成的配方（禁用）。", "jei.tooltip.error.recipe.transfer.missing": "缺少物品", "jei.tooltip.error.recipe.transfer.inventory.full": "物品栏已满。", "jei.tooltip.error.recipe.transfer.no.server": "服务器必须安装 JEI。", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "配方太大，无法在2x2玩家合成方格中合成。", "jei.tooltip.error.crash": "获取提示时崩溃，请查看客户端了解详细日志。", "jei.tooltip.error.render.crash": "渲染时崩溃，请查看客户端了解详细日志。", "jei.chat.error.no.cheat.permission.1": "你没有使用 JEI 作弊模式的权限。", "jei.chat.error.no.cheat.permission.disabled": "在该服务器，所有玩家都无法使用 JEI 的“作弊模式”。", "jei.chat.error.no.cheat.permission.enabled": "在该服务器，以下类型的玩家可以使用 JEI 的“作弊模式”：", "jei.chat.error.no.cheat.permission.creative": "处于创造模式的玩家", "jei.chat.error.no.cheat.permission.op": "拥有管理员身份的玩家（/op）", "jei.chat.error.no.cheat.permission.give": "可以使用 /give 命令的玩家", "jei.key.category.overlays": "JEI（悬浮界面）", "key.jei.toggleOverlay": "显示/隐藏 JEI 悬浮界面", "key.jei.focusSearch": "高亮搜索框", "key.jei.previousPage": "上一页", "key.jei.nextPage": "下一页", "key.jei.toggleBookmarkOverlay": "显示/隐藏加入书签的物品", "jei.key.category.recipe.gui": "JEI（配方）", "key.jei.recipeBack": "上一个配方", "key.jei.previousCategory": "上一个配方分类", "key.jei.nextCategory": "下一个配方分类", "key.jei.previousRecipePage": "上一页配方", "key.jei.nextRecipePage": "下一页配方", "key.jei.closeRecipeGui": "关闭配方界面", "jei.key.category.cheat.mode": "JEI（作弊模式）", "key.jei.toggleCheatMode": "切换作弊模式", "key.jei.cheatOneItem": "作弊获得1个物品", "key.jei.cheatOneItem2": "作弊获得1个物品", "key.jei.cheatItemStack": "作弊获得1组", "key.jei.cheatItemStack2": "作弊获得1组", "jei.key.category.hover.config.button": "JEI（将鼠标悬停在配置按钮上）", "key.jei.toggleCheatModeConfigButton": "切换作弊模式", "jei.key.category.edit.mode": "JEI（编辑模式）", "key.jei.toggleEditMode": "编辑模式开关", "key.jei.toggleHideIngredient": "隐藏原料", "key.jei.toggleWildcardHideIngredient": "隐藏原料（通配符）", "jei.key.category.mouse.hover": "JEI（鼠标悬停）", "key.jei.bookmark": "添加/删除书签", "key.jei.showRecipe": "显示物品配方", "key.jei.showRecipe2": "显示物品配方", "key.jei.showUses": "显示物品用途", "key.jei.showUses2": "显示物品用途", "key.jei.transferRecipeBookmark": "合成书签合成表（一次）", "key.jei.maxTransferRecipeBookmark": "合成书签合成表（最大数量）", "jei.key.category.search": "JEI（搜索过滤器）", "key.jei.clearSearchBar": "清除搜索过滤器", "key.jei.previousSearch": "上一个搜索", "key.jei.nextSearch": "下一个搜索", "jei.key.category.dev.tools": "JEI（开发者工具）", "key.jei.copy.recipe.id": "复制配方ID到剪贴板", "jei.config": "JEI 配置", "jei.config.name": "名称：%s", "jei.config.description": "描述：%s", "jei.config.valueValues": "有效数值：%s", "jei.config.defaultValue": "默认值：%s", "jei.config.title": "%MODNAME 配置", "jei.config.default": "默认", "jei.config.valid": "有效", "jei.config.mode": "模式", "jei.config.mode.description": "更改 JEI 的运行模式。", "jei.config.mode.cheatItemsEnabled": "作弊模式", "jei.config.mode.cheatItemsEnabled.description": "直接获取物品而非显示配方。", "jei.config.mode.editEnabled": "隐藏物品模式", "jei.config.mode.editEnabled.description": "通过点击物品列表覆盖层中的物品来隐藏或显示它们。", "jei.config.interface": "界面", "jei.config.interface.description": "与用户界面相关的选项。", "jei.config.interface.overlayEnabled": "显示物品列表覆盖层", "jei.config.interface.overlayEnabled.description": "在打开的 GUI 旁显示物品列表覆盖层。", "jei.config.interface.bookmarkOverlayEnabled": "显示书签列表覆盖层", "jei.config.interface.bookmarkOverlayEnabled.description": "在打开的 GUI 旁显示书签列表覆盖层。", "jei.config.client.appearance": "外观", "jei.config.client.appearance.description": "用于更改 JEI 外观的配置选项。", "jei.config.client.appearance.centerSearch": "搜索栏居中", "jei.config.client.appearance.centerSearch.description": "将 JEI 搜索栏移至屏幕底部中央。", "jei.config.client.appearance.recipeGuiHeight": "配方界面高度", "jei.config.client.appearance.recipeGuiHeight.description": "配方界面的最大高度（以像素为单位）。", "jei.config.client.cheating": "作弊设置", "jei.config.client.cheating.description": "与作弊功能相关的配置选项。", "jei.config.client.cheating.giveMode": "获取模式", "jei.config.client.cheating.giveMode.description": "选择 JEI 是直接将物品放入物品栏还是通过鼠标拾取。", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled": "通过快捷键将物品作弊到快捷栏", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled.description": "启用通过 Shift + 数字键 将物品作弊到快捷栏的功能。", "jei.config.client.cheating.showHiddenIngredients": "显示隐藏物品", "jei.config.client.cheating.showHiddenIngredients.description": "启用显示创意菜单中未包含的物品。", "jei.config.client.cheating.showTagRecipesEnabled": "显示标签配方", "jei.config.client.cheating.showTagRecipesEnabled.description": "显示物品标签和方块标签等标签对应的配方。", "jei.config.client.bookmarks": "书签", "jei.config.client.bookmarks.description": "与物品和配方书签功能相关的配置选项。", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "书签置顶添加", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "启用时，新书签会添加到列表前端；禁用时，添加到列表末尾。", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled": "拖拽重排书签", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled.description": "启用通过拖拽来重排书签列表中的项目。", "jei.config.client.tooltips": "工具提示", "jei.config.client.tooltips.description": "与 JEI 工具提示相关的配置选项。", "jei.config.client.tooltips.bookmarkTooltipFeatures": "书签工具提示功能", "jei.config.client.tooltips.bookmarkTooltipFeatures.description": "书签工具提示的额外功能。", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures": "Shift 显示书签工具提示", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures.description": "按住 Shift 键显示书签工具提示功能。", "jei.config.client.tooltips.showCreativeTabNamesEnabled": "显示创意标签名称", "jei.config.client.tooltips.showCreativeTabNamesEnabled.description": "在物品工具提示中显示创意标签名称。", "jei.config.client.tooltips.tagContentTooltipEnabled": "显示标签内容", "jei.config.client.tooltips.tagContentTooltipEnabled.description": "浏览配方物品时，在工具提示中显示标签内容。", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled": "隐藏单物品标签内容", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled.description": "当标签中仅包含一个物品时，隐藏工具提示中的标签内容。", "jei.config.client.lookups": "查询设置", "jei.config.client.lookups.description": "与 JEI 中物品用途和配方查询相关的配置选项。", "jei.config.client.lookups.lookupFluidContentsEnabled": "查询流体内容", "jei.config.client.lookups.lookupFluidContentsEnabled.description": "查询包含流体的物品配方时，同时查询对应流体的配方。", "jei.config.client.lookups.lookupBlockTagsEnabled": "查询物品方块标签", "jei.config.client.lookups.lookupBlockTagsEnabled.description": "搜索物品标签时，同时包含物品对应默认方块的标签。", "jei.config.client.input": "输入设置", "jei.config.client.input.description": "与 JEI 输入相关的配置选项。", "jei.config.client.input.dragDelayInMilliseconds": "拖拽延迟", "jei.config.client.input.dragDelayInMilliseconds.description": "长按鼠标多久后判定为拖拽操作（毫秒）。", "jei.config.client.input.smoothScrollRate": "平滑滚动速度", "jei.config.client.input.smoothScrollRate.description": "平滑滚动区域中鼠标滚轮的滚动速度（以像素为单位）。", "jei.config.client.performance": "性能设置", "jei.config.client.performance.description": "与 JEI 性能优化相关的配置选项。", "jei.config.client.performance.lowMemorySlowSearchEnabled": "低内存搜索模式", "jei.config.client.performance.lowMemorySlowSearchEnabled.description": "将搜索设置为低内存模式（搜索速度较慢但占用内存更少）。", "jei.config.client.advanced": "高级设置", "jei.config.client.advanced.description": "用于更改 JEI 功能方式的高级配置选项。", "jei.config.client.advanced.catchRenderErrorsEnabled": "捕获渲染错误", "jei.config.client.advanced.catchRenderErrorsEnabled.description": "捕获模组物品的渲染错误并尝试恢复，而非导致崩溃。", "jei.config.client.sorting": "排序设置", "jei.config.client.sorting.description": "与 JEI 配方和物品排序方式相关的配置选项。", "jei.config.client.sorting.ingredientSortStages": "物品排序阶段", "jei.config.client.sorting.ingredientSortStages.description": "物品列表的排序顺序。", "jei.config.client.sorting.recipeSorterStages": "配方排序阶段", "jei.config.client.sorting.recipeSorterStages.description": "显示配方的排序顺序。", "jei.config.client.search": "搜索设置", "jei.config.client.search.description": "与 JEI 配方搜索方式相关的配置选项。", "jei.config.client.search.modNameSearchMode": "@模组名称搜索模式", "jei.config.client.search.modNameSearchMode.description": "模组名称搜索模式（前缀：@）。", "jei.config.client.search.tagSearchMode": "#标签搜索模式", "jei.config.client.search.tagSearchMode.description": "标签搜索模式（前缀：#）。", "jei.config.client.search.tooltipSearchMode": "$工具提示搜索模式", "jei.config.client.search.tooltipSearchMode.description": "工具提示搜索模式（前缀：$）。", "jei.config.client.search.colorSearchMode": "^颜色搜索模式", "jei.config.client.search.colorSearchMode.description": "颜色搜索模式（前缀：^）。", "jei.config.client.search.resourceLocationSearchMode": "&资源位置搜索模式", "jei.config.client.search.resourceLocationSearchMode.description": "资源位置搜索模式（前缀：&）。", "jei.config.client.search.creativeTabSearchMode": "%创意标签搜索模式", "jei.config.client.search.creativeTabSearchMode.description": "创意模式标签名称搜索模式（前缀：%）。", "jei.config.client.search.searchAdvancedTooltips": "搜索高级工具提示", "jei.config.client.search.searchAdvancedTooltips.description": "在高级工具提示中搜索（通过 F3 + H 启用）。", "jei.config.client.search.searchModIds": "搜索模组ID", "jei.config.client.search.searchModIds.description": "除模组名称外，同时搜索模组ID。", "jei.config.client.search.searchModAliases": "搜索模组别名", "jei.config.client.search.searchModAliases.description": "除模组名称外，同时搜索插件添加的模组别名（替代名称）。", "jei.config.client.search.searchShortModNames": "搜索模组简称", "jei.config.client.search.searchShortModNames.description": "通过模组名称的首字母缩写进行搜索。", "jei.config.client.search.searchIngredientAliases": "搜索物品别名", "jei.config.client.search.searchIngredientAliases.description": "除物品名称外，同时搜索插件添加的物品别名（替代名称）。", "jei.config.client.ingredientList": "物品列表", "jei.config.client.ingredientList.description": "与物品列表（屏幕右侧的物品列表）相关的配置选项", "jei.config.client.ingredientList.maxRows": "最大行数", "jei.config.client.ingredientList.maxRows.description": "显示的最大行数。", "jei.config.client.ingredientList.maxColumns": "最大列数", "jei.config.client.ingredientList.maxColumns.description": "显示的最大列数。", "jei.config.client.ingredientList.horizontalAlignment": "水平对齐", "jei.config.client.ingredientList.horizontalAlignment.description": "物品列表在可用区域内的水平对齐方式。", "jei.config.client.ingredientList.verticalAlignment": "垂直对齐", "jei.config.client.ingredientList.verticalAlignment.description": "物品列表在可用区域内的垂直对齐方式。", "jei.config.client.ingredientList.buttonNavigationVisibility": "导航可见性", "jei.config.client.ingredientList.buttonNavigationVisibility.description": "顶部页面按钮的可见性。选择 AUTO_HIDE 可仅在存在多页时显示。", "jei.config.client.ingredientList.drawBackground": "绘制界面背景", "jei.config.client.ingredientList.drawBackground.description": "启用此选项可在物品列表后方绘制背景纹理。", "jei.config.client.bookmarkList": "书签列表", "jei.config.client.bookmarkList.description": "与书签列表（屏幕左侧的书签物品列表）相关的配置选项", "jei.config.client.bookmarkList.maxRows": "最大行数", "jei.config.client.bookmarkList.maxRows.description": "显示的最大行数。", "jei.config.client.bookmarkList.maxColumns": "最大列数", "jei.config.client.bookmarkList.maxColumns.description": "显示的最大列数。", "jei.config.client.bookmarkList.horizontalAlignment": "水平对齐", "jei.config.client.bookmarkList.horizontalAlignment.description": "书签列表在可用区域内的水平对齐方式。", "jei.config.client.bookmarkList.verticalAlignment": "垂直对齐", "jei.config.client.bookmarkList.verticalAlignment.description": "书签列表在可用区域内的垂直对齐方式。", "jei.config.client.bookmarkList.buttonNavigationVisibility": "导航可见性", "jei.config.client.bookmarkList.buttonNavigationVisibility.description": "顶部页面按钮的可见性。选择 AUTO_HIDE 可仅在存在多页时显示。", "jei.config.client.bookmarkList.drawBackground": "绘制界面背景", "jei.config.client.bookmarkList.drawBackground.description": "启用此选项可在书签列表后方绘制背景纹理。", "jei.config.client.advanced.itemBlacklist": "物品黑名单", "jei.config.client.advanced.itemBlacklist.description": "不应在物品列表覆盖层中显示的物品列表。格式：modId[:name[:meta]]。隐藏物品模式会自动在此添加或移除条目。", "jei.config.client.advanced.maxColumns": "最大覆盖层宽度", "jei.config.client.advanced.maxColumns.description": "物品列表和书签列表覆盖层的最大宽度。", "jei.config.modIdFormat.modName": "模组名称", "jei.config.modIdFormat.modName.description": "与模组名称显示相关的配置选项", "jei.config.modIdFormat.modName.modNameFormat": "模组名称格式", "jei.config.modIdFormat.modName.modNameFormat.description": "JEI 界面工具提示中模组名称的格式。留空则禁用。", "jei.config.debug.debug": "调试", "jei.config.debug.debug.description": "帮助开发者调试 JEI 问题的配置选项", "jei.config.debug.debug.debugMode": "调试模式", "jei.config.debug.debug.debugMode.description": "启用调试模式", "jei.config.debug.debug.debugGuis": "调试界面", "jei.config.debug.debug.debugGuis.description": "启用界面调试模式", "jei.config.debug.debug.debugInputs": "调试输入", "jei.config.debug.debug.debugInputs.description": "启用输入调试模式", "jei.config.debug.debug.debugInfoTooltipsEnabled": "调试信息工具提示", "jei.config.debug.debug.debugInfoTooltipsEnabled.description": "启用高级工具提示时，在物品工具提示中添加调试信息。", "jei.config.debug.debug.crashingTestItemsEnabled": "启用崩溃测试物品", "jei.config.debug.debug.crashingTestItemsEnabled.description": "向 JEI 添加故意导致崩溃的物品，以帮助调试 JEI。", "jei.config.debug.debug.logSuffixTreeStats": "记录搜索树统计信息", "jei.config.debug.debug.logSuffixTreeStats.description": "记录用于搜索的后缀树信息，以帮助调试 JEI。", "jei.config.colors.colors": "颜色设置", "jei.config.colors.colors.description": "与 JEI 中物品颜色搜索相关的配置选项", "jei.config.colors.colors.searchColors": "搜索颜色", "jei.config.colors.colors.searchColors.description": "要搜索的颜色值。", "gui.jei.editMode.description": "JEI 隐藏材料模式：", "gui.jei.editMode.description.hide": "按下 %s 隐藏。", "gui.jei.editMode.description.hide.wild": "按下 %s 通过通配符隐藏。", "gui.jei.category.craftingTable": "合成", "gui.jei.category.stoneCutter": "切石", "gui.jei.category.smelting": "烧炼", "gui.jei.category.smoking": "烟熏", "gui.jei.category.blasting": "熔炼", "gui.jei.category.campfire": "营火烹饪", "gui.jei.category.smelting.experience": "%s XP", "gui.jei.category.smelting.time.seconds": "%ss", "gui.jei.category.smelting_fuel": "烧炼燃料", "gui.jei.category.smoking_fuel": "烟熏燃料", "gui.jei.category.blasting_fuel": "熔炼燃料", "gui.jei.category.fuel.smeltCount.single": "可烧炼1个物品", "gui.jei.category.fuel.smeltCount": "可烧炼 %s 个物品", "gui.jei.category.brewing": "酿造", "gui.jei.category.brewing.steps": "步骤：%s", "gui.jei.category.compostable": "堆肥", "gui.jei.category.compostable.chance": "概率：%s%%", "gui.jei.category.grindstone": "研磨", "gui.jei.category.grindstone.experience": "%s 至 %s XP", "gui.jei.category.itemInformation": "信息", "gui.jei.category.tagInformation": "%s 标签", "gui.jei.category.tagInformation.block": "方块标签", "gui.jei.category.tagInformation.fluid": "流体标签", "gui.jei.category.tagInformation.item": "物品标签", "gui.jei.category.recipe.crashed": "此配方已崩溃。请查看客户端日志了解详情。", "jei.message.configured": "安装 Configured 模组以访问游戏内配置", "jei.message.config.folder": "或点击此处打开 JEI 配置文件夹", "jei.message.copy.recipe.id.success": "以下配方ID已复制到剪贴板：%s", "jei.message.copy.recipe.id.failure": "无法复制配方ID到剪贴板，因为ID未知", "jei.message.missing.recipes.from.server": "JEI 缺少配方。请在服务器上安装 JEI 以将配方同步到客户端。\n自 Minecraft 1.21.2 起，配方存储在服务器而非客户端。", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "jei.key.shift": "SHIFT", "jei.key.mouse.left": "鼠标左键", "jei.key.mouse.right": "鼠标右键", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}