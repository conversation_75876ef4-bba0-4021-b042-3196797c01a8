package mezz.jei.common.gui.textures;

import mezz.jei.api.constants.ModIds;
import mezz.jei.api.gui.drawable.IDrawableStatic;
import mezz.jei.common.gui.elements.ScalableDrawable;
import mezz.jei.common.gui.elements.DrawableSprite;
import mezz.jei.common.gui.elements.HighResolutionDrawable;
import net.minecraft.resources.ResourceLocation;

public class Textures {
	private final JeiGuiSpriteManager guiSpriteManager;

	private final IDrawableStatic slot;
	private final IDrawableStatic outputSlot;
	private final ScalableDrawable recipeCatalystSlotBackground;
	private final ScalableDrawable ingredientListSlotBackground;
	private final ScalableDrawable bookmarkListSlotBackground;
	private final IDrawableStatic tabSelected;
	private final IDrawableStatic tabUnselected;
	private final ScalableDrawable recipeGuiBackground;
	private final ScalableDrawable ingredientListBackground;
	private final ScalableDrawable bookmarkListBackground;
	private final ScalableDrawable recipeBackground;
	private final ScalableDrawable recipePreviewBackground;
	private final ScalableDrawable searchBackground;
	private final ScalableDrawable scrollbarBackground;
	private final ScalableDrawable scrollbarMarker;

	private final HighResolutionDrawable shapelessIcon;
	private final IDrawableStatic arrowPrevious;
	private final IDrawableStatic arrowNext;
	private final IDrawableStatic recipeTransfer;
	private final IDrawableStatic recipeBookmark;
	private final IDrawableStatic configButtonIcon;
	private final IDrawableStatic configButtonCheatIcon;
	private final IDrawableStatic bookmarkButtonDisabledIcon;
	private final IDrawableStatic bookmarkButtonEnabledIcon;
	private final IDrawableStatic infoIcon;
	private final ScalableDrawable catalystTab;
	private final ScalableDrawable recipeOptionsTab;
	private final IDrawableStatic flameIcon;
	private final IDrawableStatic flameEmptyIcon;
	private final IDrawableStatic recipeArrow;
	private final IDrawableStatic recipeArrowFilled;
	private final IDrawableStatic recipePlusSign;
	private final IDrawableStatic bookmarksFirst;
	private final IDrawableStatic craftableFirst;

	private final IDrawableStatic brewingStandBackground;
	private final IDrawableStatic brewingStandBlazeHeat;
	private final IDrawableStatic brewingStandBubbles;
	private final IDrawableStatic brewingStandArrow;

	public Textures(JeiGuiSpriteManager guiSpriteManager) {
		this.guiSpriteManager = guiSpriteManager;

		this.slot = createGuiSprite("slot", 18, 18);
		this.outputSlot = createGuiSprite("output_slot", 26, 26);
		this.recipeCatalystSlotBackground = createScalableGuiSprite("recipe_catalyst_slot_background");
		this.ingredientListSlotBackground = createScalableGuiSprite("ingredient_list_slot_background");
		this.bookmarkListSlotBackground = createScalableGuiSprite("bookmark_list_slot_background");
		this.tabSelected = createGuiSprite("tab_selected", 24, 24);
		this.tabUnselected = createGuiSprite("tab_unselected", 24, 24);
		this.recipeGuiBackground = createScalableGuiSprite("gui_background");
		this.ingredientListBackground = createScalableGuiSprite("ingredient_list_background");
		this.bookmarkListBackground = createScalableGuiSprite("bookmark_list_background");
		this.recipeBackground = createScalableGuiSprite("single_recipe_background");
		this.recipePreviewBackground = createScalableGuiSprite("recipe_preview_background");
		this.searchBackground = createScalableGuiSprite("search_background");
		this.scrollbarBackground = createScalableGuiSprite("scrollbar_background");
		this.scrollbarMarker = createScalableGuiSprite("scrollbar_marker");
		this.catalystTab = createScalableGuiSprite("catalyst_tab");
		this.recipeOptionsTab = createScalableGuiSprite("recipe_options_tab");
		this.recipeArrow = createGuiSprite("recipe_arrow", 22, 16);
		this.recipeArrowFilled = createGuiSprite("recipe_arrow_filled", 22, 16);
		this.recipePlusSign = createGuiSprite("recipe_plus_sign", 13, 13);

		this.brewingStandBackground = createGuiSprite("brewing_stand_background", 64, 60);
		this.brewingStandBlazeHeat = createGuiSprite("brewing_stand_blaze_heat", 18, 4);
		this.brewingStandBubbles = createGuiSprite("brewing_stand_bubbles", 11, 28);
		this.brewingStandArrow = createGuiSprite("brewing_stand_arrow", 7, 27);

		DrawableSprite rawShapelessIcon = createGuiSprite("icons/shapeless_icon", 36, 36)
			.trim(1, 2, 1, 1);
		this.shapelessIcon = new HighResolutionDrawable(rawShapelessIcon, 4);

		this.arrowPrevious = createGuiSprite("icons/arrow_previous", 9, 9)
			.trim(0, 0, 1, 1);
		this.arrowNext = createGuiSprite("icons/arrow_next", 9, 9)
			.trim(0, 0, 1, 1);
		this.recipeTransfer = createGuiSprite("icons/recipe_transfer", 7, 7);
		this.recipeBookmark = createGuiSprite("icons/recipe_bookmark", 9, 9);
		this.configButtonIcon = createGuiSprite("icons/config_button", 16, 16);
		this.configButtonCheatIcon = createGuiSprite("icons/config_button_cheat", 16, 16);
		this.bookmarkButtonDisabledIcon = createGuiSprite("icons/bookmark_button_disabled", 16, 16);
		this.bookmarkButtonEnabledIcon = createGuiSprite("icons/bookmark_button_enabled", 16, 16);
		this.infoIcon = createGuiSprite("icons/info", 16, 16);
		this.flameIcon = createGuiSprite("icons/flame", 14, 14);
		this.flameEmptyIcon = createGuiSprite("icons/flame_empty", 14, 14);
		this.bookmarksFirst = createGuiSprite("icons/bookmarks_first", 16, 16);
		this.craftableFirst = createGuiSprite("icons/craftable_first", 16, 16);
	}

	private ResourceLocation createSprite(String name) {
		return ResourceLocation.fromNamespaceAndPath(ModIds.JEI_ID, name);
	}

	private DrawableSprite createGuiSprite(String name, int width, int height) {
		ResourceLocation location = createSprite(name);
		return new DrawableSprite(guiSpriteManager, location, width, height);
	}

	private ScalableDrawable createScalableGuiSprite(String name) {
		ResourceLocation location = createSprite(name);
		return new ScalableDrawable(guiSpriteManager, location);
	}

	public IDrawableStatic getSlot() {
		return slot;
	}

	public IDrawableStatic getOutputSlot() {
		return outputSlot;
	}

	public IDrawableStatic getTabSelected() {
		return tabSelected;
	}

	public IDrawableStatic getTabUnselected() {
		return tabUnselected;
	}

	public HighResolutionDrawable getShapelessIcon() {
		return shapelessIcon;
	}

	public IDrawableStatic getArrowPrevious() {
		return arrowPrevious;
	}

	public IDrawableStatic getArrowNext() {
		return arrowNext;
	}

	public IDrawableStatic getRecipeTransfer() {
		return recipeTransfer;
	}

	public IDrawableStatic getRecipeBookmark() {
		return recipeBookmark;
	}

	public IDrawableStatic getBookmarksFirst() {
		return bookmarksFirst;
	}

	public IDrawableStatic getCraftableFirst() {
		return craftableFirst;
	}

	public IDrawableStatic getConfigButtonIcon() {
		return configButtonIcon;
	}

	public IDrawableStatic getConfigButtonCheatIcon() {
		return configButtonCheatIcon;
	}

	public IDrawableStatic getBookmarkButtonDisabledIcon() {
		return bookmarkButtonDisabledIcon;
	}

	public IDrawableStatic getBookmarkButtonEnabledIcon() {
		return bookmarkButtonEnabledIcon;
	}

	public ScalableDrawable getRecipeGuiBackground() {
		return recipeGuiBackground;
	}

	public ScalableDrawable getIngredientListBackground() {
		return ingredientListBackground;
	}

	public ScalableDrawable getBookmarkListBackground() {
		return bookmarkListBackground;
	}

	public ScalableDrawable getRecipeBackground() {
		return recipeBackground;
	}

	public ScalableDrawable getRecipePreviewBackground() {
		return recipePreviewBackground;
	}

	public ScalableDrawable getSearchBackground() {
		return searchBackground;
	}

	public IDrawableStatic getInfoIcon() {
		return infoIcon;
	}

	public ScalableDrawable getCatalystTab() {
		return catalystTab;
	}

	public ScalableDrawable getRecipeOptionsTab() {
		return recipeOptionsTab;
	}

	public IDrawableStatic getRecipeArrow() {
		return recipeArrow;
	}

	public IDrawableStatic getRecipeArrowFilled() {
		return recipeArrowFilled;
	}

	public IDrawableStatic getRecipePlusSign() {
		return recipePlusSign;
	}

	public ScalableDrawable getRecipeCatalystSlotBackground() {
		return recipeCatalystSlotBackground;
	}

	public ScalableDrawable getIngredientListSlotBackground() {
		return ingredientListSlotBackground;
	}

	public ScalableDrawable getBookmarkListSlotBackground() {
		return bookmarkListSlotBackground;
	}

	public IDrawableStatic getFlameIcon() {
		return flameIcon;
	}

	public IDrawableStatic getFlameEmptyIcon() {
		return flameEmptyIcon;
	}

	public ScalableDrawable getScrollbarMarker() {
		return scrollbarMarker;
	}

	public ScalableDrawable getScrollbarBackground() {
		return scrollbarBackground;
	}

	public IDrawableStatic getBrewingStandBackground() {
		return brewingStandBackground;
	}

	public IDrawableStatic getBrewingStandBlazeHeat() {
		return brewingStandBlazeHeat;
	}

	public IDrawableStatic getBrewingStandBubbles() {
		return brewingStandBubbles;
	}

	public IDrawableStatic getBrewingStandArrow() {
		return brewingStandArrow;
	}

	public JeiGuiSpriteManager getGuiSpriteManager() {
		return guiSpriteManager;
	}
}
