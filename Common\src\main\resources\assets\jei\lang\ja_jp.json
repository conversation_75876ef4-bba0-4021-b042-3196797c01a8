{"_comment": "Debug (for a debug mode, do not need translation)", "jei.tooltip.config": "JEIの設定", "jei.tooltip.show.recipes": "レシピを表示", "jei.tooltip.show.all.recipes.hotkey": "「%s」を押してすべてのレシピを表示", "jei.tooltip.delete.item": "クリックで削除", "jei.tooltip.liquid.amount.with.capacity": "%s／%s mB", "jei.tooltip.liquid.amount": "%s mB", "jei.tooltip.liquid.flowing": "%s流", "jei.tooltip.transfer": "アイテムを配置", "jei.tooltip.recipe.tag": "以下のすべてで：%s", "jei.tooltip.item.colors": "色：%s", "jei.tooltip.item.search.aliases": "検索エイリアス：", "jei.tooltip.shapeless.recipe": "不定形レシピ", "jei.tooltip.cheat.mode.button.enabled": "チートモードが有効", "jei.tooltip.cheat.mode.how.to.disable.hotkey": "「%s」を押して切り替え", "jei.tooltip.cheat.mode.how.to.disable.hover.config.button.hotkey": "「%s」を押して切り替え", "jei.tooltip.recipe.by": "%sによるレシピ", "jei.tooltip.recipe.id": "レシピID：%s", "jei.tooltip.not.enough.space": "JEIを表示するためのスペースが狭すぎます", "jei.tooltip.ingredient.list.disabled": "JEIオーバーレイは無効です", "jei.tooltip.ingredient.list.disabled.how.to.fix": "「%s」を押して有効化", "jei.tooltip.bookmarks": "JEIブックマーク", "jei.tooltip.bookmarks.usage.nokey": "操作設定でJEIブックマークのキー割り当てを追加してください", "jei.tooltip.bookmarks.usage.key": "アイテムに重ねて「%s」キーを押してブックマークします", "jei.tooltip.bookmarks.not.enough.space": "JEIブックマークを表示するためのスペースが狭すぎます", "jei.tooltip.bookmarks.recipe": "ブックマークしたレシピ（%s）", "jei.tooltip.bookmarks.recipe.add": "レシピをブックマークに追加", "jei.tooltip.bookmarks.recipe.remove": "レシピをブックマークから削除", "jei.tooltip.bookmarks.tooltips.usage": "［「%s」を押して詳細を表示］", "jei.tooltip.bookmarks.tooltips.transfer.usage": "［「%s」を押して一回分クラフト］", "jei.tooltip.bookmarks.tooltips.transfer.max.usage": "［「%s」を押して作れるだけクラフト］", "jei.tooltip.recipe.sort.bookmarks.first.enabled": "ブックマークしたレシピを優先表示（有効）", "jei.tooltip.recipe.sort.bookmarks.first.disabled": "ブックマークしたレシピを優先表示（無効）", "jei.tooltip.recipe.sort.craftable.first.enabled": "クラフト可能なレシピを優先表示（有効）", "jei.tooltip.recipe.sort.craftable.first.disabled": "クラフト可能なレシピを優先表示（無効）", "jei.tooltip.error.recipe.transfer.missing": "アイテムがインベントリ内に存在しません", "jei.tooltip.error.recipe.transfer.inventory.full": "インベントリが満杯です", "jei.tooltip.error.recipe.transfer.no.server": "サーバーにJEIが導入されている必要があります", "jei.tooltip.error.recipe.transfer.too.large.player.inventory": "2x2のクラフトグリッドではクラフトできないサイズのレシピです", "jei.tooltip.error.crash": "このアイテムのツールチップの取得中にクラッシュが発生しました。詳細はクライアントのログを参照してください", "jei.tooltip.error.render.crash": "このアイテムのレンダリング中にクラッシュが発生しました。詳細はクライアントのログを参照してください", "jei.chat.error.no.cheat.permission.1": "JEIのチートモードを使用する権限がありません", "jei.chat.error.no.cheat.permission.disabled": "このサーバーでは、JEIのチートモードはすべてのプレイヤーに対して無効化されています", "jei.chat.error.no.cheat.permission.enabled": "このサーバーでは、以下のプレイヤーのみがJEIのチートモードを使用できます：", "jei.chat.error.no.cheat.permission.creative": "クリエイティブモードのプレイヤー", "jei.chat.error.no.cheat.permission.op": "管理者権限（/op）を持つプレイヤー", "jei.chat.error.no.cheat.permission.give": "「/give」コマンドを使用できるプレイヤー", "jei.key.category.overlays": "JEI（オーバーレイ）", "key.jei.toggleOverlay": "JEIの表示／非表示", "key.jei.focusSearch": "検索バーの選択", "key.jei.previousPage": "前のページへ", "key.jei.nextPage": "次のページへ", "key.jei.toggleBookmarkOverlay": "ブックマークの表示／非表示", "jei.key.category.recipe.gui": "JEI（レシピ）", "key.jei.recipeBack": "前のレシピへ", "key.jei.previousCategory": "前のレシピカテゴリへ", "key.jei.nextCategory": "次のレシピカテゴリへ", "key.jei.previousRecipePage": "前のレシピページへ", "key.jei.nextRecipePage": "次のレシピページへ", "key.jei.closeRecipeGui": "レシピを閉じる", "jei.key.category.cheat.mode": "JEI（チートモード）", "key.jei.toggleCheatMode": "チートモードの切り替え", "key.jei.cheatOneItem": "アイテムを1個取得", "key.jei.cheatOneItem2": "アイテムを1個取得", "key.jei.cheatItemStack": "アイテムを1スタック取得", "key.jei.cheatItemStack2": "アイテムを1スタック取得", "jei.key.category.hover.config.button": "JEI（設定ボタンにマウスを重ねたとき）", "key.jei.toggleCheatModeConfigButton": "チートモードの切り替え", "jei.key.category.edit.mode": "JEI（アイテム隠蔽モード）", "key.jei.toggleEditMode": "アイテム隠蔽モードの切り替え", "key.jei.toggleHideIngredient": "アイテムを隠す", "key.jei.toggleWildcardHideIngredient": "アイテムを隠す（ワイルドカード）", "jei.key.category.mouse.hover": "JEI（マウスを重ねたとき）", "key.jei.bookmark": "ブックマークの追加／削除", "key.jei.showRecipe": "レシピの表示", "key.jei.showRecipe2": "レシピの表示", "key.jei.showUses": "用途の表示", "key.jei.showUses2": "用途の表示", "key.jei.transferRecipeBookmark": "ブックマークしたレシピでクラフト（一回分）", "key.jei.maxTransferRecipeBookmark": "ブックマークしたレシピでクラフト（作れるだけ）", "jei.key.category.search": "JEI（検索フィルター）", "key.jei.clearSearchBar": "検索フィルターをクリア", "key.jei.previousSearch": "前の検索へ", "key.jei.nextSearch": "次の検索へ", "jei.key.category.dev.tools": "JEI（開発者ツール）", "key.jei.copy.recipe.id": "レシピIDをクリップボードにコピー", "jei.config": "JEIの設定", "jei.config.name": "項目名：%s", "jei.config.description": "説明：%s", "jei.config.valueValues": "有効値：%s", "jei.config.defaultValue": "デフォルト値：%s", "jei.config.title": "%MODNAMEの設定", "jei.config.default": "デフォルト", "jei.config.valid": "有効", "jei.config.mode": "モード", "jei.config.mode.description": "JEIの動作モードを変更します。", "jei.config.mode.cheatItemsEnabled": "チートモード", "jei.config.mode.cheatItemsEnabled.description": "レシピを表示する代わりにアイテムを与えます。", "jei.config.mode.editEnabled": "アイテム隠蔽モード", "jei.config.mode.editEnabled.description": "クリックしてアイテムリストでのアイテムの非表示／表示を切り替えます。", "jei.config.interface": "インターフェース", "jei.config.interface.description": "ユーザーインターフェースに関する設定。", "jei.config.interface.overlayEnabled": "アイテムリストオーバーレイの有効化", "jei.config.interface.overlayEnabled.description": "開いているGUIの隣にアイテムリストを表示します。", "jei.config.interface.bookmarkOverlayEnabled": "ブックマークリストの表示", "jei.config.interface.bookmarkOverlayEnabled.description": "開いているGUIの隣にブックマークリストを表示します。", "jei.config.client.appearance": "外観", "jei.config.client.appearance.description": "JEIの外観に関する設定。", "jei.config.client.appearance.centerSearch": "検索バーを中央に移動", "jei.config.client.appearance.centerSearch.description": "JEIの検索バーを画面の中央下部に移動します。", "jei.config.client.appearance.recipeGuiHeight": "レシピGUIの最大高さ", "jei.config.client.appearance.recipeGuiHeight.description": "レシピGUIの最大高さ（ピクセル）。", "jei.config.client.cheating": "チート", "jei.config.client.cheating.description": "チートに関する設定。", "jei.config.client.cheating.giveMode": "付与モード", "jei.config.client.cheating.giveMode.description": "JEIがアイテムをインベントリに直接与えるか、マウスで拾うかを選択します", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled": "ホットキーを使用してホットバーにチートアイテムを追加", "jei.config.client.cheating.cheatToHotbarUsingHotkeysEnabled.description": "Shift + 数値キーを使用して、ホットバーにチートアイテムを与えられるようになります。", "jei.config.client.cheating.showHiddenIngredients": "隠されたアイテムを表示", "jei.config.client.cheating.showHiddenIngredients.description": "クリエイティブメニューに無いアイテムを表示します。", "jei.config.client.cheating.showTagRecipesEnabled": "タグレシピの表示", "jei.config.client.cheating.showTagRecipesEnabled.description": "アイテムタグやブロックタグなどのタグレシピを表示します。", "jei.config.client.bookmarks": "ブックマーク", "jei.config.client.bookmarks.description": "アイテムやレシピのブックマークに関する設定。", "jei.config.client.bookmarks.addBookmarksToFrontEnabled": "ブックマークを先頭に追加", "jei.config.client.bookmarks.addBookmarksToFrontEnabled.description": "trueにすると、新しいブックマークをリストの先頭に追加します。falseにすると、末尾に追加します。", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled": "ドラッグでブックマークを並び替える", "jei.config.client.bookmarks.dragToRearrangeBookmarksEnabled.description": "ブックマークをドラッグして、リスト内で並び替えられるようになります。", "jei.config.client.tooltips": "ツールチップ", "jei.config.client.tooltips.description": "JEIのツールチップに関する設定。", "jei.config.client.tooltips.bookmarkTooltipFeatures": "ブックマークのツールチップ機能", "jei.config.client.tooltips.bookmarkTooltipFeatures.description": "ブックマークのツールチップの追加機能。", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures": "Shiftでブックマークのツールチップを表示", "jei.config.client.tooltips.holdShiftToShowBookmarkTooltipFeatures.description": "Shiftを押すとブックマークのツールチップ機能を表示します。", "jei.config.client.tooltips.showCreativeTabNamesEnabled": "クリエイティブタブ名の表示", "jei.config.client.tooltips.showCreativeTabNamesEnabled.description": "アイテムのツールチップにクリエイティブタブ名を表示します。", "jei.config.client.tooltips.tagContentTooltipEnabled": "タグの表示", "jei.config.client.tooltips.tagContentTooltipEnabled.description": "レシピの材料を確認するときに、ツールチップにタグの内容を表示します。", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled": "単一アイテム用タグの内容を非表示", "jei.config.client.tooltips.hideSingleTagContentTooltipEnabled.description": "タグにアイテムが1種類しかない場合、ツールチップにタグの内容を表示しないようにします。", "jei.config.client.lookups": "確認", "jei.config.client.lookups.description": "JEIのアイテムの用途やレシピの確認に関する設定。", "jei.config.client.lookups.lookupFluidContentsEnabled": "流体の確認", "jei.config.client.lookups.lookupFluidContentsEnabled.description": "流体を含むアイテムのレシピを検索する場合、その流体のレシピも合わせて検索します。", "jei.config.client.lookups.lookupBlockTagsEnabled": "アイテム／ブロックタグの確認", "jei.config.client.lookups.lookupBlockTagsEnabled.description": "アイテムタグを検索する場合、そのアイテムに含まれるデフォルトのブロックタグも含めて表示します。", "jei.config.client.input": "入力", "jei.config.client.input.description": "JEIの入力に関する設定。", "jei.config.client.input.dragDelayInMilliseconds": "ドラッグの遅延", "jei.config.client.input.dragDelayInMilliseconds.description": "マウスを長押しするとマウスのドラッグとみなされるまでのミリ秒数。", "jei.config.client.input.smoothScrollRate": "滑らかなスクロール速度", "jei.config.client.input.smoothScrollRate.description": "滑らかなスクロールのスクロールボックスでマウスホイールを使用する場合のスクロール速度。ピクセル単位で測定されます。", "jei.config.client.performance": "パフォーマンス", "jei.config.client.performance.description": "JEIのパフォーマンスの最適化に関する設定。", "jei.config.client.performance.lowMemorySlowSearchEnabled": "省メモリ検索", "jei.config.client.performance.lowMemorySlowSearchEnabled.description": "検索を省メモリモードに設定します（検索は遅くなりますが、RAMの使用量が低下します）。", "jei.config.client.advanced": "高度な設定", "jei.config.client.advanced.description": "JEIの機能を変更する高度な設定。", "jei.config.client.advanced.catchRenderErrorsEnabled": "レンダリングエラーのキャッチ", "jei.config.client.advanced.catchRenderErrorsEnabled.description": "クラッシュさせる代わりに、Modのアイテムのレンダリングエラーをキャッチし、回復を試みるようになります。", "jei.config.client.sorting": "ソート", "jei.config.client.sorting.description": "JEIがレシピやアイテムをソートする方法に関する設定。", "jei.config.client.sorting.ingredientSortStages": "アイテムのソート階層", "jei.config.client.sorting.ingredientSortStages.description": "アイテムリストのソート順序。", "jei.config.client.sorting.recipeSorterStages": "レシピのソート階層", "jei.config.client.sorting.recipeSorterStages.description": "表示されるレシピのソート順序。", "jei.config.client.search": "検索", "jei.config.client.search.description": "JEIがレシピを検索する方法に関する設定。", "jei.config.client.search.modNameSearchMode": "検索モード（@Mod名）", "jei.config.client.search.modNameSearchMode.description": "Mod名での検索モード（接頭辞：@）。", "jei.config.client.search.tagSearchMode": "検索モード（#タグ）", "jei.config.client.search.tagSearchMode.description": "タグでの検索モード （接頭辞：#）。", "jei.config.client.search.tooltipSearchMode": "検索モード（$ツールチップ）", "jei.config.client.search.tooltipSearchMode.description": "ツールチップでの検索モード（接頭辞：$）", "jei.config.client.search.colorSearchMode": "検索モード（^色）", "jei.config.client.search.colorSearchMode.description": "色での検索モード（接頭辞：^）。", "jei.config.client.search.resourceLocationSearchMode": "検索モード（&リソースの場所）", "jei.config.client.search.resourceLocationSearchMode.description": "リソースの場所での検索モード（接頭辞：&）。", "jei.config.client.search.creativeTabSearchMode": "検索モード（%クリエイティブタブ）", "jei.config.client.search.creativeTabSearchMode.description": "クリエイティブタブ名での検索モード（接頭辞：%）。", "jei.config.client.search.searchAdvancedTooltips": "高度な情報での検索", "jei.config.client.search.searchAdvancedTooltips.description": "高度な情報での検索（F3 + Hで表示）。", "jei.config.client.search.searchModIds": "Mod IDでの検索", "jei.config.client.search.searchModIds.description": "Mod名に加え、Mod IDでの検索を行います。", "jei.config.client.search.searchModAliases": "Modエイリアスでの検索", "jei.config.client.search.searchModAliases.description": "Mod名に加え、プラグインにより追加されたModエイリアス（代替名）での検索を行います。", "jei.config.client.search.searchShortModNames": "省略したMod名での検索", "jei.config.client.search.searchShortModNames.description": "Mod名の頭文字での検索を行います。", "jei.config.client.search.searchIngredientAliases": "アイテムエイリアスでの検索", "jei.config.client.search.searchIngredientAliases.description": "アイテム名に加え、プラグインにより追加されたアイテムエイリアス（代替名）での検索を行います。", "jei.config.client.ingredientList": "アイテムリスト", "jei.config.client.ingredientList.description": "アイテムリスト（画面右側のリスト）に関する設定。", "jei.config.client.ingredientList.maxRows": "最大行数", "jei.config.client.ingredientList.maxRows.description": "表示される行の最大数。", "jei.config.client.ingredientList.maxColumns": "最大列数", "jei.config.client.ingredientList.maxColumns.description": "表示される列の最大数。", "jei.config.client.ingredientList.horizontalAlignment": "水平方向の配置", "jei.config.client.ingredientList.horizontalAlignment.description": "利用可能なエリア内でのアイテムリストの水平方向の配置。", "jei.config.client.ingredientList.verticalAlignment": "垂直方向の配置", "jei.config.client.ingredientList.verticalAlignment.description": "利用可能なエリア内でのアイテムリストの垂直方向の配置。", "jei.config.client.ingredientList.buttonNavigationVisibility": "ナビゲーションの表示", "jei.config.client.ingredientList.buttonNavigationVisibility.description": "上部のページボタンの表示設定。ページが複数ある場合のみ表示するにはAUTO_HIDEを使用してください。", "jei.config.client.ingredientList.drawBackground": "GUIの背景を描画", "jei.config.client.ingredientList.drawBackground.description": "trueにすると、アイテムリストの背後に背景テクスチャを描画します。", "jei.config.client.bookmarkList": "ブックマークリスト", "jei.config.client.bookmarkList.description": "ブックマークリスト（画面左側のリスト）に関する設定。", "jei.config.client.bookmarkList.maxRows": "最大行数", "jei.config.client.bookmarkList.maxRows.description": "表示される行の最大数。", "jei.config.client.bookmarkList.maxColumns": "最大列数", "jei.config.client.bookmarkList.maxColumns.description": "表示される列の最大数。", "jei.config.client.bookmarkList.horizontalAlignment": "水平方向の配置", "jei.config.client.bookmarkList.horizontalAlignment.description": "利用可能なエリア内でのブックマークリストの水平方向の配置。", "jei.config.client.bookmarkList.verticalAlignment": "垂直方向の配置", "jei.config.client.bookmarkList.verticalAlignment.description": "利用可能なエリア内でのブックマークリストの垂直方向の配置。", "jei.config.client.bookmarkList.buttonNavigationVisibility": "ナビゲーションの表示", "jei.config.client.bookmarkList.buttonNavigationVisibility.description": "上部のページボタンの表示設定。ページが複数ある場合のみ表示するにはAUTO_HIDEを使用してください。", "jei.config.client.bookmarkList.drawBackground": "GUIの背景を描画", "jei.config.client.bookmarkList.drawBackground.description": "trueにすると、ブックマークリストの背後に背景テクスチャを描画します。", "jei.config.client.advanced.itemBlacklist": "アイテムのブラックリスト", "jei.config.client.advanced.itemBlacklist.description": "アイテムリストに表示しないアイテムのリスト。形式：modId[:name[:meta]]。アイテム隠蔽モードでは、ここにアイテムが自動的に追加／削除されます。", "jei.config.client.advanced.maxColumns": "オーバーレイの最大幅", "jei.config.client.advanced.maxColumns.description": "アイテム／ブックマークリストのオーバーレイの最大幅。", "jei.config.modIdFormat.modName": "Mod名", "jei.config.modIdFormat.modName.description": "Mod名の表示に関する設定。", "jei.config.modIdFormat.modName.modNameFormat": "Mod名の表示形式", "jei.config.modIdFormat.modName.modNameFormat.description": "JEIのGUIのツールチップのMod名の表示形式。無効化するには空白のままにしてください。", "jei.config.debug.debug": "デバッグ", "jei.config.debug.debug.description": "開発者がJEIの問題をデバッグするのに役立つ設定。", "jei.config.debug.debug.debugMode": "デバッグモード", "jei.config.debug.debug.debugMode.description": "デバッグモードを有効化します。", "jei.config.debug.debug.debugGuis": "デバッグGUI", "jei.config.debug.debug.debugGuis.description": "デバッグGUIモードを有効化します。", "jei.config.debug.debug.debugInputs": "デバッグ入力", "jei.config.debug.debug.debugInputs.description": "デバッグ入力モードを有効化します。", "jei.config.debug.debug.debugInfoTooltipsEnabled": "デバッグ説明ツールチップ", "jei.config.debug.debug.debugInfoTooltipsEnabled.description": "高度な情報が有効化されている場合、アイテムのツールチップにデバッグ情報を追加します。", "jei.config.debug.debug.crashingTestItemsEnabled": "クラッシュテストアイテムの有効化", "jei.config.debug.debug.crashingTestItemsEnabled.description": "JEIのデバッグを補助するために、意図的にクラッシュを引き起こすアイテムを追加します。", "jei.config.debug.debug.logSuffixTreeStats": "統計ツリーの記録", "jei.config.debug.debug.logSuffixTreeStats.description": "JEIのデバッグを補助するために、検索に使用された接尾辞ツリーに関する情報をログに記録します。", "jei.config.colors.colors": "色", "jei.config.colors.colors.description": "JEIのアイテムの色の検索に関する設定。", "jei.config.colors.colors.searchColors": "色の検索", "jei.config.colors.colors.searchColors.description": "検索する色の値。", "gui.jei.editMode.description": "JEIアイテム隠蔽モード：", "gui.jei.editMode.description.hide": "「%s」を押して隠す", "gui.jei.editMode.description.hide.wild": "「%s」を押してワイルドカードで隠す", "gui.jei.category.craftingTable": "クラフト", "gui.jei.category.stoneCutter": "石切", "gui.jei.category.smelting": "製錬", "gui.jei.category.smoking": "燻製", "gui.jei.category.blasting": "溶鉱", "gui.jei.category.campfire": "焚き火調理", "gui.jei.category.smelting.experience": "%s経験値", "gui.jei.category.smelting.time.seconds": "%s秒", "gui.jei.category.fuel": "燃料", "gui.jei.category.fuel.smeltCount.single": "1回製錬分", "gui.jei.category.fuel.smeltCount": "%s回製錬分", "gui.jei.category.brewing": "醸造", "gui.jei.category.brewing.steps": "第%s段階", "gui.jei.category.compostable": "堆肥化", "gui.jei.category.compostable.chance": "確率：%s%%", "gui.jei.category.itemInformation": "説明", "gui.jei.category.tagInformation": "%sタグ", "gui.jei.category.tagInformation.block": "ブロックタグ", "gui.jei.category.tagInformation.fluid": "流体タグ", "gui.jei.category.tagInformation.item": "アイテムタグ", "gui.jei.category.recipe.crashed": "このレシピはクラッシュしました。詳細はクライアントのログを参照してください。", "jei.message.configured": "ゲーム内で設定にアクセスするには、\n\"Configured\" Modを導入してください", "jei.message.config.folder": "ここをクリックするとJEIの設定フォルダーを開きます", "jei.message.copy.recipe.id.success": "以下のレシピIDをクリップボードにコピーしました：%s", "jei.message.copy.recipe.id.failure": "IDが不明なため、レシピIDのクリップボードへのコピーに失敗しました", "jei.key.combo.shift": "SHIFT + %s", "jei.key.combo.control": "CTRL + %s", "jei.key.combo.command": "CMD + %s", "jei.key.combo.alt": "ALT + %s", "jei.key.shift": "SHIFT", "jei.key.mouse.left": "クリック", "jei.key.mouse.right": "右クリック", "description.jei.wooden.door.1": "Wooden doors allow you to block monsters from entering your building.\\nTesting sentences.", "description.jei.wooden.door.2": "Clicking on a door changes its state from open to closed and vice versa.", "description.jei.wooden.door.3": "Wooden doors can be opened/closed via redstone circuits.", "description.jei.debug.formatting.1": "Testing %s formatting replacements.", "description.jei.debug.formatting.2": "Testing %s %s formatting replacements.", "description.jei.debug.formatting.3": "%s nested", "jei.alias.panda.spawn.egg": "endangered", "jei.alias.villager.spawn.egg": "HMMM"}